import time
import threading
from queue import Queue

from extension.logger import logger
from user_manager import get_users_with_queue_name, save_user_2_redis_queue

user_queue = Queue()
spain_user_field = "spainUserDatas"


# @timmer
def pay_notify(user):
    time_now = time.time()
    pay_time = user.get('pay_time', None)
    if pay_time and  time_now - int(pay_time) > 60 * 20:
        logger.error(f"超时未付款:{user['passportNO']}")
        # user["status"] = "overtime"
        # save_user_2_redis_queue(user)


def send_notify_pay():
    while not user_queue.empty():
        user = user_queue.get()
        pay_notify(user)
        user_queue.task_done()


def payment_check_job(thread_count=2):
    logger.info("##预约##开始监听是否有预约成功的用户超时未付款")
    while True:
        all_users = get_users_with_queue_name(spain_user_field)
        # 筛选预约成功的用户 发送消息提醒付款
        users_wating_appointment = list(filter(lambda x: x["status"] == "ok", all_users))
        users = users_wating_appointment
        if len(users) <= 0:
            time.sleep(30)
            continue

        for user in users:
            user_queue.put(user)

        threads = []
        # 使用 for 循环创建多线程并传入参数
        for _ in range(thread_count):
            thread = threading.Thread(target=send_notify_pay, daemon=True)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        time.sleep(5)


if __name__ == "__main__":
    payment_check_job()
