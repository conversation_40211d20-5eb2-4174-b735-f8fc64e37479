import requests
import sys
import time
import datetime
import threading
from queue import Queue
from copy import deepcopy
from bs4 import BeautifulSoup

from extension.logger import logger
from user_manager import get_users_with_queue_name, move_user_2_success_queue, save_user_2_redis_queue
from user_manager import spain_user_field, spain_success_users, move_user_2_queue
from config import wx_hook_urls, url_download_appointment_pdf, headers
from tool import send_dd_msg, area_map, get_random_user_agent, get_new_proxy, save_pay_page_2_temp, extract_alert_msg
import random
from spain_visa_date_appointment import extract_app_no
from tool import update_booking_status, BOOK_STATUS

user_queue = Queue()


url_pay_notify = "https://spain.blscn.cn/CHN/payment/paymentresponse?idData="


def notify_spain_system(user):
    try:
        header = deepcopy(headers)
        header["user-agent"] = get_random_user_agent()
        session = requests.session()
        session.headers = header

        cookie_dict = user["cookies"]
        proxy = get_new_proxy()  # user["proxy"]
        proxy_dict = {"http": proxy, "https": proxy}

        session.proxies.update(proxy_dict)
        session.cookies.update(cookie_dict)
        url = url_pay_notify + user.get("idData")
        res = session.get(url, verify=False, timeout=5)
        logger.info(f"#付款结果查询# {user.get('email')}-{user.get('passportNO')}, res:{res.status_code}")
        if res.status_code == 200:
            if "Your appointment has been booked successfully" in res.text or "Payment order number already processed" in res.text:
                app_no = extract_app_no(res.text, user)
                logger.info(f"#付款成功# {user.get('chnname')} {user.get('email')} {user.get('passportNO')}, app_id:{user.get('appointment_id')}, app_no:{app_no} url:{url}")
                user["appointment_no"] = app_no
                user["status"] = "payed"
                save_user_2_redis_queue(user)
                return True
            else:
                save_pay_page_2_temp(res.text, user.get("passportNO"), user.get("email"))
                msg = extract_alert_msg(res.text)
                if "This payment is already rejected" in msg:  # 付款超时重新移回队列
                    user["status"] = "rejected"
                    # user["queue_name"] = spain_user_field
                    save_user_2_redis_queue(user)
                logger.error(f"#付款查询结果错误# {user.get('chnname')} {user.get('email')} {user.get('passportNO')} {msg}")
                return False
        elif res.status_code == 429:  # 刷新频繁需要更换IP
            user["proxy"] = get_new_proxy()
            save_user_2_redis_queue(user)
            return False
        else:  # 登录失效了
            return False
    except Exception as e:
        logger.error(f"#付款查询错误# {user.get('email')}-{user.get('passportNO')}, {e.args[0]}")
        return False


# @timmer
def pay_notify(user):
    if user["status"] == "payment":  # 等待付款
        payed_flag = notify_spain_system(user)
        if not payed_flag:
            return
    # 已经付款成功可以下载预约信
    pno = f"护照号：{user['passportNO']}"
    user_name = f"客户姓名：{user['chnname']}"
    appointment_area = f"西班牙：{area_map[user['centerCode']]}"
    user_mail = f"注册邮箱：{user['email']}"
    visa_type = f"{user['visaTypeCode']}"
    if not user.get("appointment_id"):
        send_dd_msg(" | ".join([appointment_area, user_mail, user_name, pno]) + "没有预约信ID,请查看")
        return
    url = random.choice(wx_hook_urls)
    timestamp = time.time()
    time_now = datetime.datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
    app_id = f"预约信ID：{user.get('appointment_id')}"
    app_no = f"预约号：{user.get('appointment_no','')}"
    app_day = f"日期：{user.get('appointment_day')}"
    app_time = f"时间：{user.get('appointment_time')}"
    price = f"价格：{user.get('price',0)}"
    reg = f"登记：{user.get('reg')}"
    u_from = f"from：{user.get('from')}"
    remark = f"备注：{user.get('remark')}"
    download_url = url_download_appointment_pdf + user.get("appointment_id")
    msg_string = [time_now, appointment_area, user_name, user_mail, visa_type, app_id, app_no, app_day, app_time, pno, price, reg, u_from, remark]
    ## 钉钉通知
    send_dd_msg(" | ".join(msg_string) + "。\n预约信下载地址:" + download_url)
    ## 微信通知
    res = requests.post(url, json={"msgtype": "markdown", "markdown": {"content": " | ".join(msg_string) + "\n" + f"[预约信下载]({download_url})"}})
    update_booking_status(user, BOOK_STATUS.APPOINTMENT_DOWNLOADED, "预约信可下载")
    user["status"] = "ok"
    user.pop("cookies", None)
    move_user_2_success_queue(user)
    logger.success(f"通知到微信: {'|'.join(msg_string) + '。预约信下载地址：' + download_url}, res: {res.text}")
    # logger.info("1"


def send_notify_pay():
    while not user_queue.empty():
        user = user_queue.get()
        try:
            pay_notify(user)
        except Exception as e:
            logger.error(f"#付款通知# {e.args[0]}")
        user_queue.task_done()


def start_listen(thread_count=2):
    logger.info("开始监听预约成功的用户，发送微信消息等到付款")
    while True:
        all_users = get_users_with_queue_name(spain_success_users)
        # 筛选预约成功的用户 发送消息提醒付款
        users_wating_appointment = list(filter(lambda x: x["status"] == "payment" or x["status"] == "payed", all_users))
        users = users_wating_appointment
        if len(users) <= 0:
            time.sleep(30)
            continue

        for user in users:
            user_queue.put(user)

        threads = []
        # 使用 for 循环创建多线程并传入参数
        for _ in range(4):
            thread = threading.Thread(target=send_notify_pay, daemon=True)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        time.sleep(30)


if __name__ == "__main__":
    # 获取外部参数
    args = sys.argv[1:]
    thread_count = int(args[0]) if len(args) > 0 else 2
    start_listen(thread_count)
