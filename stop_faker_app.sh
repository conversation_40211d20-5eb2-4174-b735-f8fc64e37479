#!/bin/bash

pid=$(pgrep -f main_spain_faker_users_login.py)

if [ -z "$pid" ]; then
    echo "进程未找到: main_spain_faker_users_login"
else
    echo "关闭进程: main_spain_faker_users_login,PID为: $pid"
    kill "$pid"
fi

pid=$(pgrep -f main_spain_faker_users_appointment_v2.py)

if [ -z "$pid" ]; then
    echo "进程未找到: main_spain_faker_users_appointment_v2"
else
    echo "关闭进程: main_spain_faker_users_appointment_v2,PID为: $pid"
    kill "$pid"
fi


pid=$(pgrep -f main_spain_slot_cancel.py)

if [ -z "$pid" ]; then
    echo "进程未找到: main_spain_slot_cancel"
else
    echo "关闭进程: main_spain_slot_cancel,PID为: $pid"
    kill "$pid"
fi
