import requests
import sys
import time
import threading
import json
from queue import Queue
from copy import deepcopy
from datetime import datetime

from spain_visa_appointment_date_open import book_appointment
from spain_visa_date_appointment import date_appointment
from extension.logger import logger
from tool import get_random_user_agent, send_wx_msg
from config import headers
from user_manager import save_user_2_redis_queue, get_users_with_queue_name, subscribe_redis_msg


user_queue = Queue()
spain_user_field = "spainUserDatas"


# @timmer
def date_user_appointment(user):
    header = deepcopy(headers)
    header["user-agent"] = get_random_user_agent()
    session = requests.session()
    session.headers = header

    # 登录状态为否 跳过
    if not user.get("is_login", False):
        return None, None
    
    if int(time.time()) - int(user.get("updateTime", 0)) > 1200:  # cookie过期时间是20分钟
        return None, None

    logger.info(f"##预约## {user.get('email')}-{user.get('passportNO')}")

    cookie_dict = user["cookies"]
    proxy = user["proxy"]
    proxy_dict = {"http": proxy, "https": proxy}

    session = requests.session()
    session.proxies.update(proxy_dict)
    session.cookies.update(cookie_dict)

    # 查询预约号
    flag_book, res_book = book_appointment(user, session)
    if not flag_book:
        user["is_login"] = False
    else:  # 扫号成功了，发送通知到企微
        appointment_open_flag = res_book.get("success")
        appointment_book_avalible = res_book.get("available")
        return_uri = res_book.get("returnUrl")
        # 开始预约
        if appointment_open_flag and appointment_book_avalible and return_uri:
            flag_date, res_date = date_appointment(user, session, return_uri)
            if flag_date:
                pass
            else:
                user["is_login"] = False

    save_user_2_redis_queue(user)
    return user, session


if __name__ == "__main__":
    all_users = get_users_with_queue_name()
    user = list(filter(lambda x: x["email"] == "<EMAIL>", all_users))[0]
    # user["is_login"] = False 
    user["image_id"] = "5c44a9cd-7b5e-42c9-abbb-c05e62785619" # "e600ef83-ad2e-4df3-b2fc-751c747fd505"
    user["startDate"] = "2024/10/16"
    user["endDate"] = "2024/12/30"
    date_user_appointment(user)
