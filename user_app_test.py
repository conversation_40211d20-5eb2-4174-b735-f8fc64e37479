import requests
import sys
import time
import threading
import json
from queue import Queue
from copy import deepcopy
from datetime import datetime

from extension.logger import logger
from tool import get_random_user_agent, send_wx_msg
from config import headers
from user_manager import save_user_2_redis_queue, get_users_with_queue_name, spain_user_field



url_host = "https://spain.blscn.cn/CHN/payment/paymentresponse?idData="
def notify(user):
    header = deepcopy(headers)
    header["user-agent"] = get_random_user_agent()
    session = requests.session()
    session.headers = header

    cookie_dict = user["cookies"]
    proxy = user["proxy"]
    proxy_dict = {"http": proxy, "https": proxy}

    session = requests.session()
    session.proxies.update(proxy_dict)
    session.cookies.update(cookie_dict)
    url = url_host+user.get("idData")
    res = session.get(url)
    print(res)
    
    
if __name__ == "__main__":
    all_spain_users = get_users_with_queue_name(spain_user_field)
    user_gz = list(filter(lambda x: x["email"] == "<EMAIL>", all_spain_users))
    notify(user_gz[0])
