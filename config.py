user_agent = "Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36"

headers = {
    "user-agent": user_agent,
    "cache-control": "max-age=0",
    "sec-ch-ua": "'Not)A;Brand';v='99', 'Google Chrome';v='127', 'Chromium';v='127'",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "macOS",
    "upgrade-insecure-requests": "1",
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "sec-fetch-site": "same-origin",
    "sec-fetch-mode": "navigate",
    "sec-fetch-user": "?1",
    "sec-fetch-dest": "document",
    "accept-encoding": "gzip, deflate, br, zstd",
    "accept-language": "zh-CN,zh;q=0.9",
    "priority": "u=0",
}

default_centers = ["SHANGHAI", "CHANGSHA", "HANGZHOU", "NANJING"]

center_codes = [
    "FUZHOU",
    "GUANGZHOU",
    "BEIJING",
    "HANGZHOU",
    "CHANGSHA",
    "KUNMING",
    "SHANGHAI",
    "SHENYANG",
    "XIAN",
    "JINAN",
    "CHONGQING",
    "SHENZHEN",
    "NANJING",
    "CHENGDU",
]

user_pwd = "Kq123456@"
user_pwd_2 = "Kq123456#"

email_domains_hash = "brazil_domains_hash"

url_host = "https://spain.blscn.cn"
# 下载预约信的链接
url_download_appointment_pdf = "https://spain.blscn.cn/CHN/Payment/GetAppointmentLetterByAppointmentId?appointmentId="

wx_hook_urls = [
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=290b6096-f038-4b36-a17a-bd999b4df63f",
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=aaec6551-d994-43ff-b9a9-3ded45a6a57e",
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9c9f5f17-80f4-4307-9e5d-3307119e14f2",
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=425c5aed-ce49-46b9-8622-79bfd31231d2",
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fc171c94-5512-45f4-b955-80156a877e25",
]

wx_hook_url_post = [
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f56be611-0d4e-4e4b-91e8-14a1eac201d1",
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=04c182a3-0d87-42b9-97ce-9610397546fe",
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=da13e252-3a41-41a7-8f56-e60b420811a3",
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=93d9221e-02c7-4562-885d-8ea7e7317cd9",
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5541d618-0ed7-4d83-a5eb-25e4b23d1999"
]

genderData = {"男": "2a299467-0281-4efc-bd69-0cba402d6b26", "女": "b43ee54d-885a-458f-8f2a-92cd9cf29693", "Name": "Female", "Code": "FEMALE"}
maritalStatusData = {
    "OT": {"Id": "24b87330-375a-493b-887f-93be3e73ab5c", "Name": " Other", "Code": "OT"},
    "D": {"Id": "e8319038-a568-47ea-ad5a-3381bab11fe5", "Name": "Divorced", "Code": "D"},
    "离异": {"Id": "e8319038-a568-47ea-ad5a-3381bab11fe5", "Name": "Divorced", "Code": "D"},
    "C": {"Id": "23c3d9ec-f504-4109-89b5-804189ecb4c1", "Name": "Married", "Code": "C"},
    "已婚": {"Id": "23c3d9ec-f504-4109-89b5-804189ecb4c1", "Name": "Married", "Code": "C"},
    "SE": {"Id": "81c6fe56-6370-4f58-b0b0-540aee2d1ff7", "Name": "Separated", "Code": "SE"},
    "SO": {"Id": "f738e816-1c6b-4371-a65d-3800c2354391", "Name": "Single", "Code": "SO"},
    "V": {"Id": "67d42c06-40b9-4f9a-b725-9c4494ed5388", "Name": "Widow(er)", "Code": "V"},
}

journeyPurposeData = {
    "Airport transit": {"Id": "351336f9-b83c-4eab-9676-756f110b7a78", "Name": "Airport transit", "Code": "13"},
    "Business": {"Id": "5a15a780-3e47-49c8-942e-ef755acf57fd", "Name": "Business", "Code": "01"},
    "Bussiness": {"Id": "5a15a780-3e47-49c8-942e-ef755acf57fd", "Name": "Business", "Code": "01"},
    "Cultural reasons": {"Id": "0191076d-59fd-4389-a626-a75ed744b1aa", "Name": "Cultural reasons ", "Code": "02"},
    "Medical reasons": {"Id": "0716dfa8-b50a-4498-ae80-b5a477b2e17a", "Name": "Medical reasons ", "Code": "00"},
    "Official visit": {"Id": "e15e803e-0eb6-4834-8e5f-4beccd1ea31f", "Name": "Official visit ", "Code": "05"},
    "Others": {"Id": "5abfe19e-6753-48ed-b5f2-fc1f24b127dd", "Name": "Others", "Code": "99"},
    "Sports": {"Id": "2d75e12f-dea8-4451-9998-7f043a1cdae0", "Name": "Sports", "Code": "07"},
    "Study": {"Id": "b4ec779f-08d5-4fc1-aada-aea342de2334", "Name": "Study", "Code": "11"},
    "Tourism": {"Id": "82a413f6-0e05-4ac0-8224-61432e8dfa44", "Name": "Tourism", "Code": "10"},
    "Transit": {"Id": "fe271b94-3a93-419c-8482-7153115c9ac3", "Name": "Transit", "Code": "12"},
    "Visiting family": {"Id": "f59cde68-3cb3-4237-b772-d8e9f53ba600", "Name": "Visiting family", "Code": "03"},
}

domains = [
    "brazilabxy.xyz",
    "brazilabxyz.xyz",
    "brazilabxyza.xyz",
    "brazilabxyzav.xyz",
    "brazilabxyzab.xyz",
    "brazilabxyta.xyz",
    "brazilabxytab.xyz",
    "brazilabxytac.xyz",
    "brazilabxytad.xyz",
    "brazilabxa.xyz",
    "brazilabxv.xyz",
    "brazilabxb.xyz",
    "brazilabxc.xyz",
    "brazilabxd.xyz",
    "brazilabxe.xyz",
    "brazilabxf.xyz",
    "brazilabxg.xyz",
    "brazilabxh.xyz",
    "brazilabxi.xyz",
    "brazilabxj.xyz",
    "brazilabxk.xyz",
    "brazilabxl.xyz",
    "brazilabxm.xyz",
    "brazilabxn.xyz",
    "brazilabxo.xyz",
    "brazilabxp.xyz",
    "brazilabxq.xyz",
    "brazilabxr.xyz",
    "brazilabxs.xyz",
]
