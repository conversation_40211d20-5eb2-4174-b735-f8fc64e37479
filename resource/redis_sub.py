
import sys
import os
# 添加根目录到 sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
# 现在可以导入根目录下的文件
from RedisClient import RedisClient
import time

def call_back(channel, msg):
    redis_client.unsubscribe("test_channel")
    print(channel, msg)
    for i in range(10):
        print(i,":", msg)
        time.sleep(0.5)
    redis_client.subscribe("test_channel", call_back)
    # redis_client.unsubscribe("test_channel")
    
redis_client = RedisClient(host="*************")


redis_client.subscribe("test_channel", call_back)
while True:
    # print("listenling", time.time())
    # print("订阅开始")
    time.sleep(5)
    # redis_client.unsubscribe("test_channel")
