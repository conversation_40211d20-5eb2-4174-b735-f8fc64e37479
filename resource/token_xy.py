# -*- coding: utf-8 -*-
from curl_cffi import requests
import time
import json
import threading
from RedisClientAWS import RedisClient
import random
from datetime import datetime, timedelta
from queue import Queue
import urllib.parse
from twocaptcha import TwoCaptcha
import re
solver = TwoCaptcha("a7d7d55f349865778b327a2c37300e85")
# 创建 RedisClient 实例
redis_client = RedisClient()
delegate = json.loads(redis_client.get("fast_proxy"))


def pick_random_elements(arr, num):
    if num >= len(arr):
        return arr[:]
    shuffled = arr[:]
    random.shuffle(shuffled)
    return shuffled[:num]


def remove_account(account):
    redis_client.hdel("deuLoginUserDy", account.get("email"))
    redis_client.hset("disdeuLoginUser", account.get("email"), json.dumps(account))


def get_phone_number_dy():
    url = 'http://api.uomsg.com/zc/data.php?code=getPhone&token=36b06908a52e467cab579a325ebcc951&cardType=%E5%AE%9E%E5%8D%A1'
    response = requests.get(url, impersonate="safari15_5", verify=False)
    if response.status_code == 200:
        data = response.json()
        return data
    return None


def get_code_dy(phone):
    url = f"http://api.uomsg.com/zc/data.php?code=getMsg&token=36b06908a52e467cab579a325ebcc951&phone={phone}&keyWord=VFS"
    response = requests.get(url, impersonate="safari15_5", verify=False)
    if response.status_code == 200:
        data = response.text
        print(data)
        # 使用正则表达式查找验证码
        # 提取验证码
        otp = extract_otp(data)
        if otp:
            return otp
        else:
            return None
    return None


def extract_otp(message):
    # 使用正则表达式查找 6 位数字的验证码
    match = re.search(r'\b\d{6}\b', message)
    if match:
        return match.group(0)
    return None


def get_phone(phone):
    url = f"http://api.haozhuma.com/sms/?api=getPhone&token=aeb947a7be8ae7cb858ac4b8a02e31fa2bdfef88&sid=67227&phone={phone}"
    response = requests.get(url, impersonate="safari15_5", verify=False)
    if response.status_code == 200:
        data = response.json()
        print(data)
        # 如果找到匹配项，打印验证码
        if data.get("code") == "0":
            return True
        elif data.get("msg") == "指定专属码手机号不在线":
            return "remove_phone"
        else:
            return False
    return False


def get_code(phone):
    url = f"http://api.haozhuma.com/sms/?api=getMessage&token=aeb947a7be8ae7cb858ac4b8a02e31fa2bdfef88&sid=67227&phone={phone}"
    response = requests.get(url, impersonate="safari15_5", verify=False)
    if response.status_code == 200:
        data = response.json()
        print(data)
        # 如果找到匹配项，打印验证码
        if data.get("yzm"):
            return data.get("yzm")
        else:
            return ""
    return ""


def get_otp(user):
    try:
        print(f"正在获取{user['email']}的OTP")
        taskid = attempt_create_task()
        if taskid == None:
            return False
        time.sleep(3)
        capResult = attempt_get_result(taskid)
        if capResult == None:
            capResult = solver.turnstile(
                sitekey="0x4AAAAAAACYaM3U_Dz-4DN1",
                url="https://visa.vfsglobal.com/chn/zh/deu/login",
            ).get("code")
        url = "https://lift-apicn.vfsglobal.com/user/login"
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "origin": "https://visa.vfsglobal.com",
            "referer": "http://visa.vfsglobal.com/",
        }
        data = {
            "missioncode": "deu",
            "countrycode": "chn",
            "captcha_version": "cloudflare-v1",
            "captcha_api_key": capResult,
            "username": user["email"],
            "password": "pMCdWw78HHst8Ram73KXH+Kx+i5frRDB30b7pLaxcrm6Vzjz6vOhjteou7I9LbLiX3hkjis6+VKKvU3GNquU5PLTJPL+LFS0F0+8pXcVcO5BJ5Xy3K84smvqqh6KGbqzHQoKiDB1GSVw+QYAbUMZBBxg/4UDA/eALF5ZUVJ3QXA=",
        }

        proxy = random.choice(delegate)
        proxies = {
            "http": proxy,
            "https": proxy,
        }

        response = requests.post(
            url,
            data=data,
            headers=headers,
            proxies=proxies,
            impersonate="safari15_5",
            verify=False,
        )
        if response.status_code == 200:
            print(response.text)
            res = response.json()
            if res.get("error") != None:
                remove_account(user)
                return False
            elif res.get("accessToken") != None:
                update_token_time = int(time.time())
                user["token"] = res.get("accessToken")
                user["updateTokenTime"] = update_token_time
                user["phone"] = res.get("contactNumber")
                print(f"账号{user['email']}成功获取Token:{res['accessToken']}")
                redis_client.hset(
                    "deuLoginUser", f"{user['email']}", json.dumps({**user, "dy": True})
                )
                redis_client.hset(
                    "deuLoginUserDy", f"{user['email']}", json.dumps({**user, "dy": True})
                )
                return "Token"
            else:
                user["phone"] = res.get("contactNumber")
                redis_client.hset(
                    "deuLoginUser", f"{user['email']}", json.dumps({**user, "dy": True})
                )
                redis_client.hset(
                    "deuLoginUserDy", f"{user['email']}", json.dumps({**user, "dy": True})
                )
                print(f"账号{user.get('email')}成功获取OTP", res)
                return "SMS"
        else:
            print(
                f"账号{user.get('email')}获取OTP失败，status_code: {response.status_code}"
            )
        return False
    except Exception as e:
        print(e)
        pass


def get_token(user, otp):
    try:
        print(f"正在获取{user['email']}的票据")
        url = "https://lift-apicn.vfsglobal.com/user/login"
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "origin": "https://visa.vfsglobal.com",
            "referer": "http://visa.vfsglobal.com/",
        }
        taskid = attempt_create_task()
        if taskid == None:
            return False
        time.sleep(3)
        capResult = attempt_get_result(taskid)
        if capResult == None:
            capResult = solver.turnstile(
                sitekey="0x4AAAAAAACYaM3U_Dz-4DN1",
                url="https://visa.vfsglobal.com/chn/zh/deu/login",
            ).get("code")
        data = {
            "missioncode": "deu",
            "countrycode": "chn",
            "captcha_version": "cloudflare-v1",
            "captcha_api_key": capResult,
            "otp": str(otp),
            "username": user["email"],
            "password": "pMCdWw78HHst8Ram73KXH+Kx+i5frRDB30b7pLaxcrm6Vzjz6vOhjteou7I9LbLiX3hkjis6+VKKvU3GNquU5PLTJPL+LFS0F0+8pXcVcO5BJ5Xy3K84smvqqh6KGbqzHQoKiDB1GSVw+QYAbUMZBBxg/4UDA/eALF5ZUVJ3QXA=",
        }

        proxy = random.choice(delegate)
        proxies = {
            "http": proxy,
            "https": proxy,
        }

        response = requests.post(
            url,
            data=data,
            headers=headers,
            proxies=proxies,
            impersonate="safari15_5",
            verify=False,
        )
        if response.status_code == 200:
            data = response.json()
            print(f"获取结果", data)
            if data["error"] is None:
                update_token_time = int(time.time())
                user["token"] = data["accessToken"]
                user["updateTokenTime"] = update_token_time
                user["phone"] = data.get("contactNumber")
                print(f"账号{user['email']}成功获取Token:{data['accessToken']}")
                redis_client.hset(
                    "deuLoginUser", f"{user['email']}", json.dumps({**user, "dy": True})
                )
                redis_client.hset(
                    "deuLoginUserDy", f"{user['email']}", json.dumps({**user, "dy": True})
                )
                return True
            else:
                remove_account(user)
                print(f"账号{user['email']}获取Token失败")
                return True
        else:
            print(response.status_code)
            return False
    except Exception as e:
        print(e)
        pass


def otp_dy(user, max_retries=10, retry_interval=1):
    try:

        type = attempt_get_otp(user, max_retries, retry_interval)
        if type == "Token":
            return True
        # get_phone = attempt_get_phone_dy(user.get("phone"), max_retries, retry_interval)
        # if not get_phone:
        #     print("指定手机号失败")
        #     return False
        time.sleep(5)
        otp_code = attempt_get_code_dy(user.get("phone"), max_retries, 5)
        # url = f"http://api.haozhuma.com/sms/?api=cancelRecv&token=aeb947a7be8ae7cb858ac4b8a02e31fa2bdfef88&sid=67227&phone={user.get('phone')}"
        # requests.get(url, impersonate="safari15_5", verify=False)
        if not otp_code:
            print("Failed to get SMS code after maximum retries.")
            remove_account(user)
            return False
        if otp_code == "0":
            print(f"{user.get('email')} ：号商已经屏蔽VFS, 将移除该账号")
            remove_account(user)
            return False
        if otp_code == "" or len(otp_code) != 6:
            return False
        if not attempt_validate_otp(user, otp_code, max_retries, retry_interval):
            print("Failed to validate OTP after maximum retries.")
            return False

        return True

    except Exception as e:
        print(f"An error occurred in the OTP process: {e}")
        return False


def otp(user, max_retries=10, retry_interval=1):
    try:

        type = attempt_get_otp(user, max_retries, retry_interval)
        if type == "Token":
            return True
        get_phone = attempt_get_phone(user.get("phone"), max_retries, retry_interval)

        if not get_phone:
            print("指定手机号失败")
            return False
        time.sleep(5)
        otp_code = attempt_get_code(user.get("phone"), max_retries, 5)
        url = f"http://api.haozhuma.com/sms/?api=cancelRecv&token=aeb947a7be8ae7cb858ac4b8a02e31fa2bdfef88&sid=67227&phone={user.get('phone')}"
        requests.get(url, impersonate="safari15_5", verify=False)
        if not otp_code:
            print("Failed to get SMS code after maximum retries.")
            remove_account(user)
            return False
        if otp_code == "0":
            print(f"{user.get('email')} ：号商已经屏蔽VFS, 将移除该账号")
            remove_account(user)
            return False
        if otp_code == "" or len(otp_code) != 6:
            return False
        if not attempt_validate_otp(user, otp_code, max_retries, retry_interval):
            print("Failed to validate OTP after maximum retries.")
            return False

        return True

    except Exception as e:
        print(f"An error occurred in the OTP process: {e}")
        return False


def createTask():
    try:
        url = "https://api.capmonster.cloud/createTask"
        res = requests.post(
            url,
            impersonate="safari15_5",
            json={
                "clientKey": "0da9a62496582607429e6fe981283808",
                "task": {
                    "type": "TurnstileTaskProxyless",
                    "websiteURL": "https://visa.vfsglobal.com/chn/zh/deu/login",
                    "websiteKey": "0x4AAAAAAACYaM3U_Dz-4DN1",
                },
            },
            verify=False,
        )
        if res.status_code == 200:
            data = res.json()
            if data.get("errorId") == 0:
                print(f'成功获取task, {data.get("taskId")}')
                return data.get("taskId")
        return None

    except:
        return None


def get_result(id):
    try:
        url = "https://api.capmonster.cloud/getTaskResult"
        res = requests.post(
            url,
            impersonate="safari15_5",
            json={
                "clientKey": "0da9a62496582607429e6fe981283808",
                "taskId": id,
            },
            verify=False,
        )
        if res.status_code == 200:
            data = res.json()
            print(data)
            if data.get("errorId") == 0 and data.get("status") == "ready":
                print(f'成功获取cap_token, {data.get("solution").get("token")}')
                return data.get("solution").get("token")
        return None

    except:
        return None


def attempt_get_otp(user, max_retries, retry_interval):
    for _ in range(max_retries):
        phone = get_otp(user)
        if phone:
            return phone
        time.sleep(retry_interval)
    return ""


def attempt_get_code(phone, max_retries, retry_interval):
    for _ in range(max_retries):
        otp_code = get_code(phone)
        if otp_code != "":
            return otp_code
        time.sleep(retry_interval)
    return ""


def attempt_get_code_dy(phone, max_retries, retry_interval):
    for _ in range(max_retries):
        otp_code = get_code_dy(phone)
        if otp_code != None:
            return otp_code
        time.sleep(retry_interval)
    return ""


def attempt_validate_otp(user, otp_code, max_retries, retry_interval):
    for _ in range(max_retries):
        if get_token(user, otp_code):
            return True
        time.sleep(retry_interval)
    return False


def attempt_get_phone(phone, max_retries, retry_interval):
    for _ in range(max_retries):
        phone = get_phone(phone)
        if phone:
            return phone
        time.sleep(retry_interval)
    return ""


def attempt_get_phone_dy(phone, max_retries, retry_interval):
    for _ in range(max_retries):
        phone = get_phone_number_dy(phone)
        if phone:
            return phone
        time.sleep(retry_interval)
    return ""


def attempt_create_task(max_retries=10, retry_interval=5):
    for _ in range(max_retries):
        id = createTask()
        if id:
            return id
        time.sleep(retry_interval)
    return None


def attempt_get_result(id, max_retries=10, retry_interval=5):
    for _ in range(max_retries):
        result = get_result(id)
        if result:
            return result
        time.sleep(retry_interval)
    return None


# 创建一个线程安全的队列来保存需要处理的用户
user_queue = Queue()
# 定义一个函数用于并发处理用户请求


def process_users():
    while not user_queue.empty():
        user = user_queue.get()
        otp_dy(user)
        # otp(user)
        user_queue.task_done()


def getNeedProceed(user_list):
    current_time = int(time.time())

    def filter_condition(user):
        return (
            not user.get("updateTokenTime")
            or current_time - user.get("updateTokenTime") > 5500
        ) and user.get('terry_account') == None

    need_proceed = list(filter(filter_condition, user_list))
    return need_proceed


def refresh_proxy():
    while True:
        time.sleep(60)
        global delegate
        delegate = json.loads(redis_client.get("fast_proxy"))
        print(delegate)


threading.Thread(target=refresh_proxy).start()

while True:
    loginUsers = redis_client.hgetall("deuLoginUserDy")
    need_proceed = getNeedProceed(loginUsers)

    if len(need_proceed) != 0:
        print(f"正在刷新{len(need_proceed)}个账号票据")
        # 创建一个线程安全的队列来保存需要处理的用户
        for user in need_proceed:
            user_queue.put(user)

        num_threads = 10  # 可根据需要调整线程数量
        threads = []
        for _ in range(num_threads):
            thread = threading.Thread(target=process_users)
            thread.start()
            threads.append(thread)

        for thread in threads:
            thread.join()

        # for user in need_proceed:
        #     get_token(user)
    else:
        print("暂无需要刷新的票据")
        time.sleep(10)
