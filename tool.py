import json
import random
import string
import functools
import time
import os
import requests
import base64
import urllib3
import hmac
import hashlib
import urllib.parse
import threading
from extension.logger import logger
from config import wx_hook_urls, headers, email_domains_hash, wx_hook_url_post
from datetime import datetime
from bs4 import BeautifulSoup
from copy import deepcopy
from user_manager import redis_client, get_users_with_queue_name, spain_success_users
from extension.image_helper import compress_image_to_target
import io
from PIL import Image
from enum import Enum

urllib3.disable_warnings()


class BOOK_STATUS(Enum):
    APPOINTMENT_CANCELED = "appointment_canceled"  # 预约取消
    ACCOUNT_DELETED = "account_deleted"  # 账户注销
    REGISTE_SUCCESS = "registe_success"  # 注册成功
    REGISTE_ERROR = "registe_error"  # 注册失败
    SCHEDULE_ERROR = "schedule_error"  # 预约失败
    WAITING_FOR_PAYMENT = "waiting_for_payment"  # 等待付款
    APPOINTMENT_DOWNLOADED = "appointment_downloaded"  # 预约下载
    AVATAR_FORBIDDEN = "avatar_not_available"  # 头像不可用


def update_booking_status(user, status: BOOK_STATUS, message: str = ""):
    try:
        order_id = user.get("order_id")
        if not order_id:
            return
        payment_qrcode = user.get("pay_qrcode", "")  # pay_qrcode
        download_url = ""
        if status == BOOK_STATUS.APPOINTMENT_DOWNLOADED:
            download_url = "https://spain.blscn.cn/CHN/Payment/GetAppointmentLetterByAppointmentId?appointmentId=" + user.get("appointment_id")

        update_info = {
            "order_id": order_id,
            "status": status.value,
            "message": message,
            "payment_qrcode": payment_qrcode,
            "appointment_pdf_url": download_url,
            "email": user.get("email"),
            "appointment_date": user.get("appointment_day"),
            "appointment_time": user.get("appointment_time"),
        }
        res_update = requests.post("http://120.27.241.45:5005/api/spain-booking-callback", json=update_info, timeout=5)
        logger.info(f"req:{str(update_info)}, res:{res_update.text}")
    except Exception as e:
        logger.error(e)


def get_user_log_msg(u):
    try:
        time_create = datetime.fromtimestamp(float(u.get("createTime", 0))).strftime("%Y-%m-%d")
        infos = [
            u["chnname"],
            u["centerCode"],
            u.get("visaTypeCode", ""),
            u["passportNO"],
            u.get("email", ""),
            u.get("password", ""),
            u.get("startDate", ""),
            u.get("endDate", ""),
            str(u.get("acceptVIP", "")),
            # str(u.get("price", "")),
            time_create,
        ]
        log_msg = ", ".join(infos)
        return log_msg
    except:
        return ""


def compress_image_advanced(image_data, max_size_kb=150):
    """
    高级图片压缩方法，自适应质量和尺寸

    Args:
        image_data: 图片二进制数据
        max_size_kb: 最大文件大小(KB)

    Returns:
        bytes: 压缩后的图片二进制数据
    """
    try:
        image = Image.open(io.BytesIO(image_data))

        # 处理透明度
        if image.mode in ("RGBA", "LA", "P"):
            background = Image.new("RGB", image.size, (255, 255, 255))
            if image.mode == "P":
                image = image.convert("RGBA")
            if image.mode == "RGBA":
                background.paste(image, mask=image.split()[-1])
            else:
                background.paste(image)
            image = background
        elif image.mode != "RGB":
            image = image.convert("RGB")

        max_size_bytes = max_size_kb * 1024
        original_width, original_height = image.size

        # 智能压缩策略
        def try_compress(img, quality, width=None, height=None):
            if width and height:
                img = img.resize((width, height), Image.Resampling.LANCZOS)

            output = io.BytesIO()
            img.save(output, format="JPEG", quality=quality, optimize=True)
            return output.getvalue(), output.tell()

        # 二分查找最优质量
        def find_optimal_quality(img, target_size, width=None, height=None):
            low, high = 10, 95
            best_data = None

            while low <= high:
                mid = (low + high) // 2
                data, size = try_compress(img, mid, width, height)

                if size <= target_size:
                    best_data = data
                    low = mid + 1
                else:
                    high = mid - 1

            return best_data

        # 先尝试原尺寸
        result = find_optimal_quality(image, max_size_bytes)
        if result:
            return result

        # 尝试不同的缩放比例
        for scale in [0.9, 0.8, 0.7, 0.6, 0.5, 0.4]:
            new_width = int(original_width * scale)
            new_height = int(original_height * scale)

            result = find_optimal_quality(image, max_size_bytes, new_width, new_height)
            if result:
                return result

        # 最后兜底：强制压缩到最小
        final_width = int(original_width * 0.3)
        final_height = int(original_height * 0.3)
        data, _ = try_compress(image, 10, final_width, final_height)
        return data

    except Exception as e:
        print(f"高级图片压缩失败: {str(e)}")
        return image_data


def get_user_head_img(user_info):
    try:
        passportNo = user_info.get("passportNO")
        avatar_image = user_info.get("avatar_image")
        if avatar_image:
            url = f"http://120.27.241.45:5005/api/avatar_image/{avatar_image}"
        else:
            url = f"http://*************:5000/image/{passportNo}"
        res = requests.get(url, timeout=5)
        if res.status_code != 200:
            msg_err = f"#西班牙预约# 用户头像图片未上传: {user_info.get('chnname')} {passportNo} {res.status_code}"
            logger.error(msg_err)
            send_wx_msg(msg_err)
            send_dd_msg(msg_err, passportNo)
            return None

        if len(res.content) < 200 * 1024:
            logger.info(f"头像尺寸符合:{passportNo}")
            # return convert_to_jpg(res.content)
        image_bytes = compress_image_advanced(res.content)  # png->jpg 病压缩图片尺寸
        return image_bytes
    except Exception as e:
        logger.error(f"获取用户头像错误：{e.args[0]}")
        return None


# 生成包含大小写字母数字的 随机字符串
def generate_proxy_session(length=9):
    # length = random.randint(8, 10)
    if length < 8 or length > 10:
        raise ValueError("Length must be between 8 and 10")

    characters = string.ascii_letters + string.digits
    random_string = "".join(random.choice(characters) for _ in range(length))

    return random_string


def get_new_proxy(faker=True):
    # proxy = pick_verify_code_proxy()
    proxy_id = generate_proxy_session()
    if faker:
        proxy = f"http://t15117693479940-period-15-sid-s{proxy_id}:<EMAIL>:15818"
        return proxy
    else:
        # proxy = f"http://2F1FB2BBAC34C67F-residential-country_CN-r_30m-s_{proxy_id}:<EMAIL>:24125"
        proxy = f"http://t15207538524259-period-15-sid-s{proxy_id}:<EMAIL>:15818"
        return proxy


# 获取一个邮箱验证码的代理
# 用户名 ： d2533609401
# 密码： dbhi62rh
proxy_err_msgs = []


def pick_verify_code_proxy():
    try:
        url = "https://dps.kdlapi.com/api/getdps/?secret_id=osqoqfmqmdvjjoj22qxk&signature=nzeci6wohb2e0xs4q9iys49phsr5swgz&num=1&pt=1&format=json&sep=1"
        # url = "https://dps.kdlapi.com/api/getdps/?secret_id=olpepwbim5m929uvfv21&signature=wxuc85uspdp3m65c5clmv10okinu102t&num=1&pt=1&format=json&sep=1"
        # params = {"secret_id": "olpepwbim5m929uvfv21", "signature": "wxuc85uspdp3m65c5clmv10okinu102t", "num": 1, "pt": 1, "format": "json", "sep": 1}
        ip_port = requests.get(url)
        if ip_port.status_code == 200 and len(ip_port.json()["data"]["proxy_list"]) > 0:
            ip_addr = ip_port.json()["data"]["proxy_list"][0]
            proxy = f"http://d3616549986:dbhi62rh@{ip_addr}"
            today_left_count = ip_port.json()["data"]["today_left_count"]
            if today_left_count % 100 == 0:
                logger.warning("代理IP池剩余: " + str(today_left_count) + "次")
                msg_notify = "代理IP池剩余: " + str(today_left_count) + " 次"
                send_dd_msg(msg_notify)
                if today_left_count == 1:
                    # send_wx_msg(msg_notify)
                    logger.error("无可用dps代理, 剩余0次")
            return proxy
    except Exception:
        return None


def extract_alert_msg(html_string):
    try:  # 解析并打印下错误提示
        err_soup = BeautifulSoup(html_string, "html.parser")
        alert_el = err_soup.select_one(".alert-danger")
        if not alert_el:
            alert_el = err_soup.select_one(".alert-warning")
        if not alert_el:
            alert_el = err_soup.select_one(".alert-success")
        if alert_el:
            return alert_el.text.strip()
    except Exception as e:
        logger.error(f"##放号查询## extract_alert_msg func: {e}")
    return html_string[-200:]


def save_pay_page_2_temp(html_string, p_NO="", email=""):
    # 本地保存付款html页面
    try:
        now = datetime.now()
        date_str = now.strftime("%Y-%m-%d")
        save_path = os.path.join("pay_temp", f"{date_str}_{email}_{p_NO}_待付款页面") + ".html"
        with open(save_path, "w", encoding="utf-8") as fw:
            fw.write(html_string)
    except Exception:
        logger.error(f"#预约中# {date_str}_{email}保存付款页面到本地错误")


# 计算函数耗时的装饰器
def timmer(func):
    functools.wraps(func)

    def wrapper(*args, **kw):
        t_start = time.time()
        res = func(*args, **kw)
        interval = time.time() - t_start
        print(f"{func.__name__}耗时：{round(interval, 2)}s")
        return res

    return wrapper


def cookie_string_to_dict(cookie_string):
    cookie_dict = {}
    cookies = cookie_string.split(";")
    for cookie in cookies:
        cookie = cookie.strip()
        if cookie:
            key, value = cookie.split("=", 1)
            cookie_dict[key] = value
    return cookie_dict


# @timmer
def b64_api_bypass(base64_string):
    try:
        # 去除 Base64 字符串中的前缀（如果有）
        if base64_string.startswith("data:image"):
            base64_string = base64_string.split(",")[1]

        json_data = {"bs64_img": base64_string}
        res = requests.post("http://*************:9998/ocr", json=json_data)
        code_number = res.json().get("res")
        # print(code_number)
        return code_number
    except Exception as e:
        print("验证码识别异常" + str(e))
    return None


def generate_phone_number():
    # 第一位固定为1
    first_digit = "1"
    # 第二位可以是3, 4, 5, 6, 7, 8, 9中的一个
    second_digit = random.choice(["3", "4", "5", "6", "7", "8", "9"])
    # 后面的9位数字随机生成
    rest_digits = "".join(random.choices("0123456789", k=9))
    # 组合成完整的手机号
    phone_number = first_digit + second_digit + rest_digits
    return phone_number


lock = threading.Lock()

users_doamin = [
    "nextdomain1.xyz",
    "nextdomain2.xyz",
    "nextdomain3.xyz",
    "nextdomain4.xyz",
    "nextdomain5.xyz",
    "nextdomain6.xyz",
    "nextdomain7.xyz",
    "nextdomain8.xyz",
    "nextdomain9.xyz",
    "nextdomain10.xyz",
    "nextdomain11.xyz",
    "nextdomain12.xyz",
    "nextdomain13.xyz",
    "nextdomain14.xyz",
    "nextdomain15.xyz",
    "nextdomain16.xyz",
    "nextdomain17.xyz",
    "nextdomain18.xyz",
    "nextdomain19.xyz",
    "nextdomain20.xyz",
    "nextdomain21.xyz",
    "nextdomain22.xyz",
    "nextdomain23.xyz",
    "nextdomain24.xyz",
    "nextdomain25.xyz",
    "nextdomain26.xyz",
    "nextdomain27.xyz",
    "nextdomain28.xyz",
    "nextdomain29.xyz",
    "nextdomain30.xyz",
]


# 生成随机的注册邮箱
def generate_random_email(domain="", faker=False):
    username_length = random.randint(6, 9)
    username = "".join(random.choices(string.ascii_lowercase + string.digits, k=username_length))
    user_domain = random.choice(users_doamin)
    user_email = f"{username}@{user_domain}"
    return user_email
    with lock:
        all_emails = redis_client.hgetall(email_domains_hash)
        domain = None
        for item in all_emails:
            domain = item.get("domain")
            count = item.get("count")
            if count > 2 and count < 9:
                if not faker:
                    item["count"] = count + 1
                    redis_client.hset(email_domains_hash, domain, json.dumps(item, ensure_ascii=False))
                break
            else:
                continue

        if domain:
            return f"{username}@{domain}"
        else:
            send_wx_msg("邮箱域名耗尽")
            return None


def generate_passport_number():
    pre_letter = "".join(random.choices(string.ascii_uppercase, k=4))
    # 生成随机的用户名部分，长度为10
    suff_numbers = "".join(random.choices("0123456789", k=8))

    return pre_letter + suff_numbers


def str_2_timestamp(date_str):
    # 定义字符串的格式
    date_format = "%Y-%m-%d"
    # 将字符串转换为datetime对象
    date_obj = datetime.strptime(date_str, date_format)
    # 将datetime对象转换为时间戳（秒数）
    timestamp = date_obj.timestamp()
    return timestamp


area_map = {
    "FUZHOU": "福州",
    "GUANGZHOU": "广州",
    "BEIJING": "北京",
    "HANGZHOU": "杭州",
    "CHANGSHA": "长沙",
    "KUNMING": "昆明",
    "SHANGHAI": "上海",
    "SHENYANG": "沈阳",
    "XIAN": "西安",
    "JINAN": "济南",
    "CHONGQING": "重庆",
    "SHENZHEN": "深圳",
    "NANJING": "南京",
    "CHENGDU": "成都",
}
service_mobile = ["17321213657", "15365306907"]
wx_msg_sends = {}


def send_wx_msg(msg, centerCode=None):
    try:
        # global msg_sends
        # city = area_map.get(centerCode.upper())
        if not centerCode:
            centerCode = generate_proxy_session()
        if msg not in wx_msg_sends.get(centerCode, ""):
            wx_msg_sends[centerCode] = msg
            url = random.choice(wx_hook_urls)
            res = requests.post(url, json={"msgtype": "text", "text": {"content": msg}}, timeout=5)
            logger.success(f"通知到微信: {msg}, res:{res.text}")
    except Exception as e:
        logger.error(f"微信通知失败{e}")


post_days_msg = {}


def send_wx_post_days(msg, centerCode=None):
    try:
        if msg != post_days_msg.get(centerCode, ""):
            post_days_msg[centerCode] = msg
            url = random.choice(wx_hook_url_post)
            res = requests.post(url, json={"msgtype": "text", "text": {"content": msg}}, timeout=5)
            logger.success(f"通知到微信: {msg}, res:{res.text}")
    except Exception as e:
        logger.error(f"微信通知失败{e}")


dd_msg_sends = []


def send_dd_msg(msg="", tag=""):
    if msg in dd_msg_sends:
        return
    dd_msg_sends.append(msg)
    try:
        timestamp = str(round(time.time() * 1000))
        secret = "SECfa4fa0ed78848618c5760b2b47dfcf3c3f6b2d9e18d542b0745d4b751676fa99"
        secret_enc = secret.encode("utf-8")
        string_to_sign = "{}\n{}".format(timestamp, secret)
        string_to_sign_enc = string_to_sign.encode("utf-8")
        hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))

        url = f"https://oapi.dingtalk.com/robot/send?access_token=26a07376acadb9cbad9cfc415f0af56c217db861a426defb2e55c5737a6e4bf5&timestamp={timestamp}&sign={sign}"
        response = requests.post(url, json={"msgtype": "text", "text": {"content": msg}}, timeout=5)
        logger.success(f"通知到钉钉: {msg}, res:{response.text}")
    except Exception as e:
        logger.error(f"通知到钉钉失败:{e}")


def get_random_user_agent():
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1",
    ]
    return random.choice(user_agents)


def base64_to_image(base64_string, f_number):
    # 去除 Base64 字符串中的前缀（如果有）
    try:
        if base64_string.startswith("data:image"):
            base64_string = base64_string.split(",")[1]

        # 解码 Base64 字符串
        image_data = base64.b64decode(base64_string)
        img_name = generate_proxy_session(8)
        # 将解码后的数据保存为图片文件
        with open(os.path.join("imgs", f"{f_number}_{img_name}.gif"), "wb") as f:
            f.write(image_data)
    except Exception:
        pass


def get_country_item(session, country_code="CHN"):
    country_list = [
        {"Name": "China", "Code": "CHN", "Id": "5e44cd63-68f0-41f2-b708-0eb3bf9f4a72"},
        {"Name": "Cook Islands ", "Code": "COK", "Id": "95bd5c86-6705-4154-9618-73f9ac4c0271"},
        {"Name": "DPRK (Democratic People's Republic of Korea)", "Code": "PRK", "Id": "a6369fd1-621c-4eb7-a623-dd471d727fa0"},
        {"Name": "Guinea ", "Code": "GIN", "Id": "252a3360-2d32-4745-bac1-a49a4c9cda09"},
        {"Name": "Guinea-Bissau ", "Code": "GNB", "Id": "8491beed-b25a-4194-9f60-0f1e1a5234e4"},
        {"Name": "Indonesia ", "Code": "IDN", "Id": "e6e64509-72fc-4104-af25-9d17d16fb43d"},
        {"Name": "Iran", "Code": "IRN", "Id": "2027f594-613d-47a8-9943-b1b2f31e3222"},
        {"Name": "Iraq ", "Code": "IRQ", "Id": "a90a3a85-57a1-4450-818b-bb6b71b5065a"},
        {"Name": "Ireland", "Code": "IRL", "Id": "17495bff-81a4-406b-9643-f49fcd0b275d"},
        {"Name": "Israel ", "Code": "ISR", "Id": "55cebbd4-4417-43cc-9e74-6340600ea875"},
        {"Name": "Italy", "Code": "ITA", "Id": "96d0ee2d-6a10-4a32-9423-d0fe064a7d94"},
        {"Name": "Jamaica", "Code": "JAM", "Id": "0d6445aa-b5d3-4909-99b9-437ad03ab964"},
        {"Name": "Japan", "Code": "JPN", "Id": "03e999f5-1269-4595-b560-5d79d0483477"},
        {"Name": "Jordan ", "Code": "JOR", "Id": "a9ce81e1-1551-4d92-8e9a-0bffdef5dff5"},
        {"Name": "Kazakhstan ", "Code": "KAZ", "Id": "080ebeaf-6999-4962-8ac7-8397bae4e39a"},
        {"Name": "Kenya ", "Code": "KEN", "Id": "a5c75693-fb5f-45a1-8ff7-73df6feb99ba"},
        {"Name": "Kuwait ", "Code": "KWT", "Id": "1dcde2f5-d5f9-41fc-805d-374817b07f81"},
        {"Name": "Kyrgyzstan ", "Code": "KGZ", "Id": "6020cf00-80ad-4f0c-8f9e-3b84f8b2485e"},
        {"Name": "Laos", "Code": "LAO", "Id": "7f15a89a-2427-4330-b2d3-fefa66d2337d"},
        {"Name": "Latvia", "Code": "LVA", "Id": "2412f9e2-520c-4742-aafe-321d6ded66e7"},
        {"Name": "Lebanon", "Code": "LBN", "Id": "6f6d4b27-9a87-46fa-a840-9a4aa931948a"},
        {"Name": "Lesotho", "Code": "LSO", "Id": "11e1d049-ca0c-4a18-88ce-f361ea1efd08"},
        {"Name": "Liberia", "Code": "LBR", "Id": "7619b369-28af-4edd-80e8-826ab6da5f15"},
        {"Name": "Libya", "Code": "LBY", "Id": "240f030a-bef1-4146-a61c-001922e89008"},
        {"Name": "Liechtenstein", "Code": "LIE", "Id": "9de0c55e-d93b-408e-bd8a-06a0044434b4"},
        {"Name": "Lithuania", "Code": "LTU", "Id": "4be30775-b735-495b-9527-4e8024d4c0e6"},
        {"Name": "Macau,China", "Code": "MACAU_CHINA", "Id": "058d0f36-71bb-46c8-bfaa-f3c7c777793b"},
        {"Name": "Madagascar", "Code": "MDG", "Id": "32d09a05-7340-475c-8a0a-8b50dd0ad5ce"},
        {"Name": "Malawi", "Code": "MWI", "Id": "d61d0e6a-4fc7-442b-aa89-d488a757ed19"},
        {"Name": "Malaysia", "Code": "MYS", "Id": "608f6fb7-9339-4839-bbfe-074399e48ae8"},
        {"Name": "Maldives", "Code": "MDV", "Id": "b23ff753-9ab7-4cfc-b905-f6d7507b3deb"},
        {"Name": "Mali", "Code": "MLI", "Id": "e93d2828-ea6f-40f0-8278-9117479abce7"},
        {"Name": "Malta", "Code": "MLT", "Id": "09cc99a3-7ae3-423f-b4c6-ffd71d110b50"},
        {"Name": "Mexico", "Code": "MEX", "Id": "ad6b938c-227a-4ccb-8628-5710bc7a061d"},
        {"Name": "Micronesia ", "Code": "FSM", "Id": "a67870dd-c7ed-44f4-b2a1-7af414f94a20"},
        {"Name": "Moldova", "Code": "MDA", "Id": "80b20dfc-cbae-43b7-b981-bc74ee0d12d5"},
        {"Name": "Monaco", "Code": "MCO", "Id": "97a999e3-2003-4327-8794-9ac3e90951c4"},
        {"Name": "Mongolia", "Code": "MNG", "Id": "8b92a5c0-1f5e-4875-bab7-b67fab726033"},
        {"Name": "Montenegro", "Code": "MNE", "Id": "1632d778-773d-41c8-8611-413b9e1c4562"},
        {"Name": "Mozambique", "Code": "MOZ", "Id": "d3871a22-d4ab-4128-87bc-d4078b469131"},
        {"Name": "Myanmar", "Code": "MMR", "Id": "1d61d708-7ea4-4488-932e-41e0909a5df1"},
        {"Name": "Namibia ", "Code": "NAM", "Id": "9bd001ec-f1e1-428b-bc1f-896f91a62181"},
        {"Name": "Nepal ", "Code": "NPL", "Id": "7defc327-1992-4611-b0ec-aa09b16d655a"},
        {"Name": "Netherlands", "Code": "NLD", "Id": "e51b4727-3623-4017-a15b-526987dd3479"},
        {"Name": "New Zealand", "Code": "NZL", "Id": "cffa341f-81bd-49ca-8aa5-c7192bb8bd1f"},
        {"Name": "Niger ", "Code": "NER", "Id": "f69f1bac-4dc3-4310-af0f-50c11ade621f"},
        {"Name": "Nigeria", "Code": "NGA", "Id": "72cfb135-6e34-402a-8a78-61ab1c829eaf"},
        {"Name": "Niue", "Code": "NIU", "Id": "bfbd0c25-d3a2-4196-8c28-f701bd1bd03f"},
        {"Name": "North Macedonia", "Code": "MKD", "Id": "aec272a7-3f08-4bab-8a88-b27d667ba767"},
        {"Name": "Norway", "Code": "NOR", "Id": "6d5d0970-49a6-4c65-ac4a-ba591a648abb"},
        {"Name": "Oman", "Code": "OMN", "Id": "396d436d-731c-40a2-bd11-d2b3e3296370"},
        {"Name": "Pakistan", "Code": "PAK", "Id": "43ba45b1-85c1-477e-accf-660366fadb84"},
        {"Name": "Palestine", "Code": "PSE", "Id": "6f606774-0d18-428f-b7f5-747e2eced094"},
        {"Name": "Peru", "Code": "PER", "Id": "45edcff3-a9c4-4a3f-8083-794eb925c534"},
        {"Name": "Philippines", "Code": "PHL", "Id": "2882bde4-a5c9-4a76-b330-ba43813c9d8d"},
        {"Name": "Poland", "Code": "POL", "Id": "ac7591d6-b2db-4cee-b5e3-f553cf805a3c"},
        {"Name": "Portugal", "Code": "PRT", "Id": "cfd1fc93-00cb-4e7e-8d89-7e8ad3ed060a"},
        {"Name": "Qatar", "Code": "QAT", "Id": "f6fd15d0-4751-49dc-be8b-111d9781b873"},
        {"Name": "Republic of Korea (South Korea) ", "Code": "KOR", "Id": "92cec30a-1748-4e91-aade-86cc63eed84a"},
        {"Name": "Romania", "Code": "ROU", "Id": "c263e460-9057-48f0-9144-2b557dbd835c"},
        {"Name": "Russia", "Code": "RUS", "Id": "b29deb52-dc73-4b62-a8b5-d3c3e5aa80f7"},
        {"Name": "Rwanda ", "Code": "RWA", "Id": "a49fe967-cb72-42ab-ba5e-7f27ca086047"},
        {"Name": "Samoa", "Code": "WSM", "Id": "19c9b53a-8b43-49b8-914f-e135bd9cf690"},
        {"Name": "San Marino", "Code": "SMR", "Id": "7fa4153c-59ab-41b9-b15a-9cc0d16cb251"},
        {"Name": "Sao Tome and Principe ", "Code": "STP", "Id": "cff9be80-97ce-4f92-ad89-93fbc544b647"},
        {"Name": "Saudi Arabia ", "Code": "SAU", "Id": "80b55a95-7521-4eea-b60e-48633aeae13b"},
        {"Name": "Senegal", "Code": "SEN", "Id": "6e582544-054f-4bf5-9af9-db72e42f1d92"},
        {"Name": "Seychelles", "Code": "SYC", "Id": "d1b67bb1-711c-4f1e-b5fc-8ed1c1a77596"},
        {"Name": "Sierra Leone", "Code": "SLE", "Id": "ecdf1acb-c7a5-401e-aa9a-47e47cb1c511"},
        {"Name": "Singapore ", "Code": "SGP", "Id": "b148de41-2b2f-4187-bb3f-8ae1ea6e2c4d"},
        {"Name": "Slovakia", "Code": "SVK", "Id": "23dedd8c-10be-459d-9cd6-922acadd4658"},
        {"Name": "Slovenia", "Code": "SVN", "Id": "ba1f260a-8532-4905-94d9-bfdf33a107ce"},
        {"Name": "Somalia", "Code": "SOM", "Id": "40e34419-c14f-4bec-b311-b0d0e11e264f"},
        {"Name": "South Africa", "Code": "ZAF", "Id": "adb37154-3b81-4a1e-b6ad-298f27aa6120"},
        {"Name": "South Sudan", "Code": "SSD", "Id": "61d177e4-a1c6-49c4-90f1-e50e75796c20"},
        {"Name": "Sri Lanka", "Code": "LKA", "Id": "d52cec25-d521-4c57-93bd-329da267c2f7"},
        {"Name": "Sudan", "Code": "SDN", "Id": "41f70368-3d51-4ad6-bdc0-aba1dd24082f"},
        {"Name": "Suriname", "Code": "SUR", "Id": "d3c7146c-ef95-4c81-9169-7a993becf7aa"},
        {"Name": "Sweden", "Code": "SWE", "Id": "1176a4b0-1954-4723-a5aa-ce4db6b3180c"},
        {"Name": "Switzerland", "Code": "CHE", "Id": "a70bd246-4f81-46f5-96da-af99f5ae4a81"},
        {"Name": "Syria ", "Code": "SYR", "Id": "dc6f083f-d686-4ba4-9c66-a032921f768f"},
        {"Name": "Taiwan,China", "Code": "TWN", "Id": "18ad4e86-8d55-4db3-82bc-e3665edc2259"},
        {"Name": "Tajikistan", "Code": "TJK", "Id": "7ad1602e-522f-4b81-8e0d-3efabab57085"},
        {"Name": "Tanzania ", "Code": "TZA", "Id": "54b9acc9-3f34-4174-a1fe-610b1630755f"},
        {"Name": "Thailand ", "Code": "THA", "Id": "db96bccf-f07b-47c1-8e81-3b7f451d83a9"},
        {"Name": "Togo", "Code": "TGO", "Id": "20e3147e-2f3d-47fa-9ccf-78e3ac97e0e3"},
        {"Name": "Tonga", "Code": "TON", "Id": "1e82a48e-c2f8-4013-8c73-21152e87f4d1"},
        {"Name": "Trinidad and Tobago", "Code": "TTO", "Id": "24cbcbb6-0106-4d76-bab9-d1d344b75a97"},
        {"Name": "Turkmenistan ", "Code": "TKM", "Id": "3f825bf7-756d-4837-a7b1-49265ef3cc49"},
        {"Name": "Uganda", "Code": "UGA", "Id": "a7df80dd-d386-4dea-8fd2-00a881f7b90e"},
        {"Name": "Ukraine", "Code": "UKR", "Id": "8b3007dd-87e3-46c1-9dbd-1d1e02ab1dd6"},
        {"Name": "United Arab Emirates ", "Code": "ARE", "Id": "8e39e717-7eb8-4ec9-a070-5d405b55ead3"},
        {"Name": "United Kingdom", "Code": "GBR", "Id": "4258eb75-c80f-49c3-ada3-7b7b2fb26f94"},
        {"Name": "United State Of America", "Code": "USA", "Id": "5c260608-d0fa-47ba-ab55-383a7a073478"},
        {"Name": "Uruguay", "Code": "URY", "Id": "596aede2-9e54-45cd-86ad-5687be8225e9"},
        {"Name": "Uzbekistan ", "Code": "UZB", "Id": "d12e5b78-0e7e-4fbb-9f86-8f0044218a0e"},
        {"Name": "Vanuatu", "Code": "VUT", "Id": "54156287-7eb6-4948-af7b-632e000b97ff"},
        {"Name": "Venezuela", "Code": "VEN", "Id": "1cf1e7f7-c05c-4b25-8a83-356196c5e089"},
        {"Name": "Vietnam", "Code": "VNM", "Id": "e297e71f-53cf-4c78-b7e8-10ac96edae35"},
        {"Name": "Afghanistan", "Code": "AFG", "Id": "713e181b-c0ab-4f30-b5f6-ee8be4de73ea"},
        {"Name": "Aland Islands", "Code": "ALA", "Id": "9c815a0e-a072-4ea2-8fac-175b0ff953c9"},
        {"Name": "Albania", "Code": "ALB", "Id": "4e42e61b-ca79-4411-af5c-6e60cbf2f23a"},
        {"Name": "Algeria", "Code": "DZA", "Id": "a8662331-a1df-43eb-8477-fd82f8ce928e"},
        {"Name": "American Samoa", "Code": "ASM", "Id": "ba124489-505d-4c1e-baae-885d3f14218b"},
        {"Name": "Andorra\t", "Code": "AND", "Id": "9c0262b7-5ece-404e-bd08-d0c106d6fc8f"},
        {"Name": "Angola\t", "Code": "AGO", "Id": "4551cafd-7030-41e1-8612-0919a05eb8b5"},
        {"Name": "Anguila\t", "Code": "AIA", "Id": "ee7a0e9c-000b-4e85-8845-69c0708dbdce"},
        {"Name": "Antarctica", "Code": "ATA", "Id": "c3cb73cb-addc-46a2-900c-a6de351a186f"},
        {"Name": "Antigua and Barbuda\t", "Code": "ATG", "Id": "4eca3f28-52ef-4ad9-aa8e-db0274f119db"},
        {"Name": "Argentina\t", "Code": "ARG", "Id": "1e82da04-60ef-4f21-bb45-9c753d777aff"},
        {"Name": "Armenia\t", "Code": "ARM", "Id": "7b1863d9-61ed-411c-9713-9dde4e3ae71f"},
        {"Name": "Aruba\t", "Code": "ABW", "Id": "a305379b-4e20-4c26-914c-13ce5c74fba3"},
        {"Name": "Australia\t", "Code": "AUS", "Id": "b5c7000d-308e-4a30-bee2-63d918411612"},
        {"Name": "Austria\t", "Code": "AUT", "Id": "019dfec5-10dc-4f69-ad61-f959dd1254e3"},
        {"Name": "Azerbaijan\t", "Code": "AZE", "Id": "1322c8ac-c651-4554-b438-998113ab7227"},
        {"Name": "Bahamas, The", "Code": "BHS", "Id": "21c4a061-26ba-45ca-9032-decae9dcc931"},
        {"Name": "Bahrain", "Code": "BHR", "Id": "953aade7-1bca-41fa-a8b7-9a3d3baef504"},
        {"Name": "Bangladesh", "Code": "BGD", "Id": "99da4cba-0925-4477-a78c-415ced6cb729"},
        {"Name": "Barbados", "Code": "BRB", "Id": "c3ced9b8-980a-4d36-bb4e-d20a5965dced"},
        {"Name": "Belarus", "Code": "BLR", "Id": "d025c4db-1618-4746-b82d-515684811cf1"},
        {"Name": "Belgium\t", "Code": "BEL", "Id": "7b03fe2b-0092-41cd-9303-ae8e2b6c5c15"},
        {"Name": "Belgium-Luxembourg", "Code": "BLX", "Id": "c6fadcdf-7ce9-4bdd-aedf-944829be18ad"},
        {"Name": "Belize\t", "Code": "BLZ", "Id": "682132f4-7967-44a5-982e-d97f069a12e1"},
        {"Name": "Benin\t", "Code": "BEN", "Id": "0f0d5bd0-f921-4ae4-a10e-ea0df7fd2556"},
        {"Name": "Bermuda\t", "Code": "BMU", "Id": "25c77eba-1de1-478d-b835-f9aa1f461ed2"},
        {"Name": "Bhutan\t", "Code": "BTN", "Id": "a5d253a9-07db-4486-9a4a-b1a3d7d02d6e"},
        {"Name": "Bolivia\t", "Code": "BOL", "Id": "71a74001-a8b8-4867-825c-4efd7f675613"},
        {"Name": "Bosnia and Herzegovina\t", "Code": "BIH", "Id": "ee63c82a-1d66-4407-b000-bb4ac18cb1f1"},
        {"Name": "Botswana\t", "Code": "BWA", "Id": "088bd628-635a-406d-9edc-c3d8b46682f1"},
        {"Name": "Bouvet Island", "Code": "BVT", "Id": "7cd484e1-5146-4272-90db-0d83d2a10fde"},
        {"Name": "Brazil\t", "Code": "BRA", "Id": "3840e794-70c8-49d3-a52a-c531007440ee"},
        {"Name": "British Indian Ocean Territory", "Code": "IOT", "Id": "a4044597-658a-4ffe-bb18-6e78be59e910"},
        {"Name": "British Virgin Islands\t\t", "Code": "VGB", "Id": "3d9f36d8-69c8-44b2-876c-e6e60c1f9c78"},
        {"Name": "Brunei\t", "Code": "BRN", "Id": "2e92c425-90d1-4bb5-bba9-e5574e4da787"},
        {"Name": "Brunei Darussalam", "Code": "BRN", "Id": "0927c5e6-cda6-4ac2-8962-715864e2c1d7"},
        {"Name": "Bulgaria\t", "Code": "BGR", "Id": "*************-488c-91b8-0a3d609608b2"},
        {"Name": "Burkina Faso\t", "Code": "BFA", "Id": "c67b914d-8985-4675-80d8-2a17c5c23d68"},
        {"Name": "Burundi\t", "Code": "BDI", "Id": "e899e62c-d031-4127-82d9-a7125eab2012"},
        {"Name": "Cambodia\t", "Code": "KHM", "Id": "b89a2227-58b3-402a-903e-1a4b3fffb253"},
        {"Name": "Cameroon\t", "Code": "CMR", "Id": "dc0eb97a-3efd-4ab8-9170-78511d72689c"},
        {"Name": "Canada\t", "Code": "CAN", "Id": "3029c387-e955-428a-afa1-373a7b8e3d95"},
        {"Name": "Cape Verde\t", "Code": "CPV", "Id": "ec8a91a2-6da5-443e-b871-30dc68d6563d"},
        {"Name": "Cayman Islands\t", "Code": "CYM", "Id": "8f935102-0d0d-47a7-ae80-2efe46f781ef"},
        {"Name": "Central African Republic\t", "Code": "CAF", "Id": "3337b292-6085-4b2a-b49d-5ead8025bea1"},
        {"Name": "Chad\t", "Code": "TCD", "Id": "0ab81cc2-339c-49f0-b583-4de44d304044"},
        {"Name": "Chile\t", "Code": "CHL", "Id": "3b2cedec-c991-485b-9367-e8a1c7808c62"},
        {"Name": "Christmas Island\t", "Code": "CXR", "Id": "409487a3-80c5-4883-9099-ce6a05975825"},
        {"Name": "Cocos (Keeling) Islands\t", "Code": "CCK", "Id": "c45cb13d-df34-49c9-a021-543e75ff0cbc"},
        {"Name": "Colombia\t", "Code": "COL", "Id": "bcf8e13b-e9df-4abb-b6a0-16f74778d03f"},
        {"Name": "Comoros\t", "Code": "COM", "Id": "03ce740b-828e-4b93-9953-6542dfa7fac4"},
        {"Name": "Congo (Brazzaville)", "Code": "COG", "Id": "45d9ba56-7778-41e5-893b-b8acaaf9a633"},
        {"Name": "Congo, (Kinshasa)", "Code": "COD", "Id": "8262f9e2-34c2-4902-8fb2-9a7b910af76f"},
        {"Name": "Congo, Dem. Rep.", "Code": "ZAR", "Id": "d10d3298-4964-488d-a343-288700bb1e2b"},
        {"Name": "Congo, Rep.\t", "Code": "COG", "Id": "bc092ef6-90f2-4b22-9696-f0cbfc363794"},
        {"Name": "Cook Islands\t", "Code": "COK", "Id": "479b6e06-ddfa-436e-bb3d-2229a0197576"},
        {"Name": "Costa Rica\t", "Code": "CRI", "Id": "02afd22c-047f-4eb8-9222-b971c967f1d6"},
        {"Name": "Cote d'Ivoire\t", "Code": "CIV", "Id": "f3a09ebd-0ced-4209-98e3-03580d6bd356"},
        {"Name": "Croatia\t", "Code": "HRV", "Id": "e507aabd-6a55-463c-8f3b-0303def862c7"},
        {"Name": "Cuba\t", "Code": "CUB", "Id": "14f53d45-f40a-4579-b485-698625e3e267"},
        {"Name": "Cyprus\t", "Code": "CYP", "Id": "4ed0156b-41c1-4f2c-947f-e1815c0794c8"},
        {"Name": "Czech Republic\t", "Code": "CZE", "Id": "d2c870f5-88db-41b2-bdc7-d842c41a3cfe"},
        {"Name": "Czechoslovakia\t", "Code": "CSK", "Id": "1162afe8-9c02-4ecf-8472-d77ff2a8b5a6"},
        {"Name": "Denmark\t", "Code": "DNK", "Id": "9f363c6b-7d42-4ff9-b1f2-6d33a2014274"},
        {"Name": "Djibouti\t", "Code": "DJI", "Id": "5a32d0c7-a063-4099-9780-8590bdc25a14"},
        {"Name": "Dominica\t", "Code": "DMA", "Id": "a5c15d24-3b47-4208-890f-2e7cfc885c8a"},
        {"Name": "Dominican Republic\t", "Code": "DOM", "Id": "2407feb1-15fc-4ecf-9609-ca72f62b5e02"},
        {"Name": "East Timor\t", "Code": "TMP", "Id": "a602734f-a039-46c3-908a-e34e66b03d14"},
        {"Name": "Ecuador\t", "Code": "ECU", "Id": "c3677f60-bc46-48e8-9a3c-863f1872e65e"},
        {"Name": "Egypt, Arab Rep.\t", "Code": "EGY", "Id": "a18ed957-f998-4465-a72d-8caf6cf7f595"},
        {"Name": "El Salvador\t", "Code": "SLV", "Id": "fbca0541-9b9c-4955-84da-ceb2c37e8f55"},
        {"Name": "Equatorial Guinea\t", "Code": "GNQ", "Id": "58e9af74-322b-4347-b9f7-c56cb1d31a0e"},
        {"Name": "Eritrea\t`", "Code": "ERI", "Id": "621ef9f5-d54e-4624-82a3-6a3a6f50063d"},
        {"Name": "Estonia\t", "Code": "EST", "Id": "65aabc8a-5837-4342-b124-576fed7355a1"},
        {"Name": "Ethiopia (excludes Eritrea)\t", "Code": "ETH", "Id": "94c57314-fa3b-4fe8-a194-98b235914c3c"},
        {"Name": "Ethiopia (includes Eritrea)\t", "Code": "ETF", "Id": "1b0c5718-ea8f-4f22-8692-c773fb1c86ad"},
        {"Name": "European Union\t", "Code": "EUN", "Id": "57beedf5-d472-4407-be16-db38486a7d68"},
        {"Name": "Faeroe Islands\t", "Code": "FRO", "Id": "aa9742ec-982e-4c6b-be9b-fb147e57c85d"},
        {"Name": "Falkland Island\t", "Code": "FLK", "Id": "fc58dcbb-664e-4650-b570-34b6eb536fda"},
        {"Name": "Falkland Islands (Malvinas)", "Code": "FLK", "Id": "d95a66b1-7568-4466-992d-04a34d7ad652"},
        {"Name": "Faroe Islands", "Code": "FRO", "Id": "14e2e84d-97b4-455e-bed4-653d46405768"},
        {"Name": "Fiji\t", "Code": "FJI", "Id": "59f050e4-a2f8-4a50-a121-c4f237a4c322"},
        {"Name": "Finland\t", "Code": "FIN", "Id": "487e66f6-bcb5-45d6-ae2e-3d713427ca98"},
        {"Name": "Fm Panama Cz\t", "Code": "PCZ", "Id": "1d121b96-2b1a-458e-877b-cdcaf5c474f3"},
        {"Name": "Fm Rhod Nyas\t", "Code": "ZW1", "Id": "2f1d6f37-8227-4bdd-8d7d-8456b4cb436c"},
        {"Name": "Fm Tanganyik\t", "Code": "TAN", "Id": "c4cc777b-672f-4d8b-b51c-08e2ab133cb0"},
        {"Name": "Fm Vietnam Dr\t", "Code": "VDR", "Id": "ae783159-1b91-488e-aaa9-e524e05af276"},
        {"Name": "Fm Vietnam Rp\t", "Code": "SVR", "Id": "2cc0845c-f225-4470-a783-b4409ea07ac9"},
        {"Name": "Fm Zanz-Pemb\t", "Code": "ZPM", "Id": "121de6f0-43f8-4ea5-a688-21a1d44a2f65"},
        {"Name": "Fr. So. Ant. Tr\t", "Code": "ATF", "Id": "253581c1-8b42-45c6-9500-9fc30f559103"},
        {"Name": "France\t", "Code": "FRA", "Id": "1150cb04-3aaa-4d60-a0c1-88a778e0d4f7"},
        {"Name": "Free Zones\t", "Code": "FRE", "Id": "fbc27530-c5ab-4c07-8b3c-7daf9603e608"},
        {"Name": "French Guiana\t", "Code": "GUF", "Id": "bff32ad0-2bb4-4cbc-8bb7-d14b8fa1dbd2"},
        {"Name": "French Polynesia\t", "Code": "PYF", "Id": "6cccd98f-8872-4c90-852b-6ad22c50a878"},
        {"Name": "French Southern Territories", "Code": "ATF", "Id": "005b44b6-41b0-461e-b720-0f3152e614b5"},
        {"Name": "Gabon\t", "Code": "GAB", "Id": "d36de7d6-da2e-4ddd-82e5-1519c2a9da2f"},
        {"Name": "Gambia, The\t", "Code": "GMB", "Id": "79844592-c3c3-4aa0-a48a-d6ea45505b79"},
        {"Name": "Gaza Strip\t", "Code": "GAZ", "Id": "20587610-fe8c-4509-8abd-ad7fb6638937"},
        {"Name": "Georgia\t", "Code": "GEO", "Id": "062e4577-763b-40fe-b93f-b66ea262e0d1"},
        {"Name": "German Democratic Republic\t", "Code": "DDR", "Id": "1cbd2b85-4199-4343-abda-8ce3b5520ac2"},
        {"Name": "Germany\t", "Code": "DEU", "Id": "67645f39-b8f2-482e-91e4-bf974d4a3d48"},
        {"Name": "Ghana\t", "Code": "GHA", "Id": "e188700b-94c6-417c-ba44-0264f3980237"},
        {"Name": "Gibraltar\t", "Code": "GIB", "Id": "5c6f04cf-b4b5-40b3-aa52-092fd5927370"},
        {"Name": "Greece\t", "Code": "GRC", "Id": "adcaffbb-b902-4176-9957-b7b5becabd4a"},
        {"Name": "Greenland\t", "Code": "GRL", "Id": "921649b7-54d2-466b-86d8-81be186db1f1"},
        {"Name": "Grenada\t", "Code": "GRD", "Id": "6afd37a2-8cd9-4b74-9100-b71216bd989f"},
        {"Name": "Guadeloupe\t", "Code": "GLP", "Id": "451225f1-778e-4f17-a3e0-65cea327891a"},
        {"Name": "Guam\t", "Code": "GUM", "Id": "797d75aa-4284-47ed-a9a8-dda15dfdb20d"},
        {"Name": "Guatemala\t", "Code": "GTM", "Id": "ffd2a775-53f9-4d20-99f6-1fc13904e236"},
        {"Name": "Guernsey", "Code": "GGY", "Id": "235e67a1-7a99-4f38-aa25-3f9f2a26fbd9"},
        {"Name": "Guinea\t", "Code": "GIN", "Id": "9d1a8a54-7e98-43a2-b23d-c8050333fbc6"},
        {"Name": "Guinea-Bissau\t", "Code": "GNB", "Id": "7b6abb03-5ce5-430a-8d55-07c7f2355c5e"},
        {"Name": "Guyana\t", "Code": "GUY", "Id": "77e85677-02ad-4fbd-bb30-fc41c0c736e8"},
        {"Name": "Haiti\t", "Code": "HTI", "Id": "6d2ecd9d-2f48-48fd-965d-00d69b71f86e"},
        {"Name": "Heard and Mcdonald Islands", "Code": "HMD", "Id": "da6d13dd-c7f5-42a9-8b73-6b091417ab0a"},
        {"Name": "Holy See\t", "Code": "VAT", "Id": "29a4a262-2e6d-4897-bb23-8bfed66fb400"},
        {"Name": "Honduras\t\t", "Code": "HND", "Id": "5886364c-8b30-4162-ae11-d7ed6b45e93e"},
        {"Name": "Hong Kong, China\t", "Code": "HKG", "Id": "74b368db-da5a-4ec7-8ef0-ec16e098bffb"},
        {"Name": "Hungary\t", "Code": "HUN", "Id": "1bc43e4d-83cc-4e2e-8fb6-2f77db384ce0"},
        {"Name": "Iceland\t", "Code": "ISL", "Id": "74cbdfc0-1326-4e50-aa07-10d7bcd74dce"},
        {"Name": "India", "Code": "IND", "Id": "d9772e95-fd6b-4c56-b6bf-fdb4e36a5cb7"},
        {"Name": "Mauritania", "Code": "MRT", "Id": "04f5f3a1-32ec-4562-aef5-8da043de7a6c"},
        {"Name": "Mauritius", "Code": "MUS", "Id": "57f5ee46-477e-436c-9680-b1a00997352d"},
        {"Name": "Morocco", "Code": "MAR", "Id": "0b546577-8389-4d5f-a8ae-1345a5f215e3"},
        {"Name": "Spain", "Code": "ESP", "Id": "0cf9da11-cb76-4566-81e5-8628d5488e3c"},
        {"Name": "Tunisia", "Code": "TUN", "Id": "7ab1c95c-ea29-4c22-8aa1-d1a9d9136967"},
        {"Name": "Turkey", "Code": "TUR", "Id": "1a26ff1a-0850-4600-b5db-b3cfbdb1566a"},
        {"Name": "Wallis and Futura Isl.\t", "Code": "WLF", "Id": "c5434870-6b1e-44ab-b278-d5bc0ba7044d"},
        {"Name": "Western Sahara\t", "Code": "ESH", "Id": "e71d8eb3-ae47-41b7-88da-fb9ce098433c"},
        {"Name": "World\t", "Code": "WLD", "Id": "866b8e8a-e053-4fb6-9421-9a8ba2b7cdc6"},
        {"Name": "Yemen Democratic\t\t", "Code": "YDR", "Id": "9179e355-a23b-4c42-9738-9b56d61eb42d"},
        {"Name": "Yemen, Rep.\t", "Code": "YEM", "Id": "394e6184-650a-4a22-94cc-69cc6c5f6588"},
        {"Name": "Yugoslavia", "Code": "SER", "Id": "10a67d22-74d5-4bd6-a065-663b83b4d8d8"},
        {"Name": "Yugoslavia\t", "Code": "SER", "Id": "9cfa5ec4-1b4d-498a-a5f0-9c915b5a0bb1"},
        {"Name": "Yugoslavia, FR (Serbia/Montene\t", "Code": "YUG", "Id": "6bdf8c78-edcc-4066-8a84-d26ea32913e3"},
        {"Name": "Zambia\t", "Code": "ZMB", "Id": "6bcedb56-3878-4683-98c8-3432d7aa15c5"},
        {"Name": "Zimbabwe\t", "Code": "ZWE", "Id": "21cd6ad9-bf3f-47a3-a31f-faa85a6f60eb"},
    ]
    if not country_list:
        res = session.get("https://spain.blscn.cn/CHN/query/GetCountryList", verify=False, timeout=15)
        if res.status_code == 200:
            country_list = res.json()
        country = list(filter(lambda x: x.get("Code").upper() == country_code, country_list))
        return country[0] if len(country) > 0 else None
    else:
        country = list(filter(lambda x: x.get("Code").upper() == country_code.upper(), country_list))
        return country[0] if len(country) > 0 else None


def get_new_session():
    session = requests.session()
    ua = get_random_user_agent()
    header = deepcopy(headers)
    header["user-agent"] = ua
    proxy = get_new_proxy()
    proxy_dict = {"http": proxy, "https": proxy}
    session.headers.update(header)
    session.proxies.update(proxy_dict)
    return session


if __name__ == "__main__":
    # all_faker_users = get_users_with_queue_name(spain_success_users)
    # all_faker_users = sorted(all_faker_users, key=lambda x: x["centerCode"])
    # for i, u in enumerate(all_faker_users):
    #     if u.get("passportNO") in ["*********"]:
    #         update_booking_status(u, BOOK_STATUS.APPOINTMENT_DOWNLOADED, "预约成功")
    # pick_verify_code_proxy()
    # send_wx_msg("测试")
    get_user_head_img(
        {
            "birthday": "1982/05/07",
            "chnname": "杨勇",
            "endDate": "2025-08-31",
            "expiredDT": "2033/02/21",
            "gender": "男",
            "name": "YONG",
            "passportNO": "*********",
            "phone": "***********",
            "startDate": "2025-08-15",
            "visaTypeCode": "Business",
            "xing": "YANG",
            "remark": "",
            "vip": 4,
            "status": "update_appointment",
            "acceptND": 2,
            "acceptVIP": 1,
            "countryCode": "CHN",
            "from": "paopaoyan",
            "signLocation": "江苏/JIANGSU",
            "bornplace": "江苏/JIANGSU",
            "passportDate": "2023/02/22",
            "maritalStatus": "C",
            "travelDate": "2025/10/18",
            "missionCode": "spain",
            "centerCode": "SHANGHAI",
            "createTime": 1753174004,
            "order_id": "2025072216464429066",
            "passport_image": "6e996899138642e08b5cba44beab853f.jpg",
            "avatar_image": "",
            "queue_name": "spainUserDatas",
            "email": "<EMAIL>",
            "BirthCountry": "5e44cd63-68f0-41f2-b708-0eb3bf9f4a72",
            "PassportType": "0a152f62-b7b2-49ad-893e-b41b15e2bef3",
            "is_login": True,
            "updateTime": 1753175178,
            "GenderId": "2a299467-0281-4efc-bd69-0cba402d6b26",
            "MaritalStatusId": "23c3d9ec-f504-4109-89b5-804189ecb4c1",
            "PurposeOfJourneyId": "5a15a780-3e47-49c8-942e-ef755acf57fd",
            "dateVIP": False,
            "date_retry": 0,
            "password": "Kq123456@",
        }
    )
    # generate_random_email()
    # b64_api_bypass(
    #     "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"
    # )
