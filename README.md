# SpainVisa

西班牙签证

服务器: *************
密码： !Jzd870318

代理：https://dps.kdlapi.com/api/getdps/?secret_id=oyf1lzxzrn8m66j8z5nz&signature=yxjpycxnq2h7td4u4xf80yqcwy8qqsuk&num=1&pt=1&format=json&sep=1
用户名 ： d2533609401
密码： dbhi62rh  
http://用户名:密码@IP:port

github.****************************************

## 用户头像存放路径 /root/upload

# TODO
1. 扫号图片验证码识别错误。


## 脚本自动启动
```crontab -e```
#### 内容格式
```
* * * * * command_to_be_executed
- - - - -
| | | | |
| | | | ----- Day of week (0 - 7) (Sunday=0 or 7)
| | | ------- Month (1 - 12)
| | --------- Day of month (1 - 31)
| ----------- Hour (0 - 23)
------------- Minute (0 - 59)
```
#### 查看名称 systemctl list-unit-files | grep cron

#### 开机启动
``` sudo systemctl enable crond ```


## 2025-07-07 TODO LIST
1. 状态变化调用接口
2. 注册完成检查用户是否存在，不存在就注销
3. 随机用户密码，根据用户信息设置

## 2025-07-11
1. 收到放号信息持续预约
2. 添加虚拟用户验证真实用户头像是否可用

## 2025-07-20
1. 头像状态同步后台
2. 扫号策略修改按照真实用户分布来确定
