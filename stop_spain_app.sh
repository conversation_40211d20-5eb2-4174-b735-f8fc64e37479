#!/bin/bash

pid=$(pgrep -f main_spain_users_login_v2.py)

if [ -z "$pid" ]; then
    echo "进程未找到: main_spain_users_login_v2"
else
    echo "找到进程: main_spain_users_login_v2, PID为: $pid"
    kill "$pid"
    echo "进程已关闭: main_spain_users_login_v2"
fi

pid=$(pgrep -f main_spain_users_date_appointment.py)

if [ -z "$pid" ]; then
    echo "进程未找到: main_spain_users_date_appointment"
else
    echo "关闭进程: main_spain_users_date_appointment, PID为: $pid"
    kill "$pid"
fi

pid=$(pgrep -f main_spain_users_registe.py)

if [ -z "$pid" ]; then
    echo "进程未找到: main_spain_users_registe"
else
    echo "关闭进程: main_spain_users_registe, PID为: $pid"
    kill "$pid"
fi


pid=$(pgrep -f main_spain_notify_pay.py)

if [ -z "$pid" ]; then
    echo "进程未找到: main_spain_notify_pay"
else
    echo "关闭进程: main_spain_notify_pay, PID为: $pid"
    kill "$pid"
fi

pid=$(pgrep -f main_spain_account_delete.py)

if [ -z "$pid" ]; then
    echo "进程未找到: main_spain_account_delete"
else
    echo "关闭进程: main_spain_account_delete, PID为: $pid"
    kill "$pid"
fi

pid=$(pgrep -f main_spain_users_date_app_optimeized.py)

if [ -z "$pid" ]; then
    echo "进程未找到: main_spain_users_date_app_optimeized"
else
    echo "关闭进程: main_spain_users_date_app_optimeized, PID为: $pid"
    kill "$pid"
fi

