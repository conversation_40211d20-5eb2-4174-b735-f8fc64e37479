import re
from bs4 import BeautifulSoup
from collections import defaultdict
from extension.logger import logger
import os
from datetime import datetime
import time
import random
import json
from config import url_host
from bypass_verify_code import bypass_verify_code_func

applicantId = 0
rspData = []
locationData = [
    {"Id": "9187", "Name": "Kunming", "Code": "KUNMING", "MissionId": "3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},
    {"Id": "9188", "Name": "Hangzhou", "Code": "HANGZHOU", "MissionId": "235b19fd-9fce-438f-be0a-18275fd0b64d"},
    {"Id": "9189", "Name": "Beijing", "Code": "BEIJING", "MissionId": "d133459a-6482-45ed-bd00-5ff32aa8b71b"},
    {"Id": "9190", "Name": "<PERSON><PERSON>", "Code": "WUHA<PERSON>", "MissionId": "d133459a-6482-45ed-bd00-5ff32aa8b71b"},
    {"Id": "9191", "Name": "Guangzhou", "Code": "GUANGZHOU", "MissionId": "3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},
    {"Id": "9192", "Name": "Fuzhou", "Code": "FUZHOU", "MissionId": "3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},
    {"Id": "9193", "Name": "Changsha", "Code": "CHANGSHA", "MissionId": "3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},
    {"Id": "9194", "Name": "Nanjing", "Code": "NANJING", "MissionId": "235b19fd-9fce-438f-be0a-18275fd0b64d"},
    {"Id": "9195", "Name": "Chengdu", "Code": "CHENGDU", "MissionId": "d133459a-6482-45ed-bd00-5ff32aa8b71b"},
    {"Id": "9196", "Name": "Xi'an", "Code": "XIAN", "MissionId": "d133459a-6482-45ed-bd00-5ff32aa8b71b"},
    {"Id": "9197", "Name": "Chongqing", "Code": "CHONGQING", "MissionId": "d133459a-6482-45ed-bd00-5ff32aa8b71b"},
    {"Id": "9198", "Name": "Shenyang", "Code": "SHENYANG", "MissionId": "d133459a-6482-45ed-bd00-5ff32aa8b71b"},
    {"Id": "9199", "Name": "Shenzhen", "Code": "SHENZHEN", "MissionId": "3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},
    {"Id": "9200", "Name": "Shanghai", "Code": "SHANGHAI", "MissionId": "235b19fd-9fce-438f-be0a-18275fd0b64d"},
    {"Id": "9201", "Name": "Jinan", "Code": "JINAN", "MissionId": "d133459a-6482-45ed-bd00-5ff32aa8b71b"},
]
visasubIdData = [
    {"Id": "5412", "Name": "TEL Visa", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "TEL_VISA"},
    {"Id": "5413", "Name": "RLD", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "RLD_VISA_VAC"},
    {"Id": "5414", "Name": "TRP", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "TRT_VISA_VAC"},
    {"Id": "5417", "Name": "SSU Visa", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "SSU_VISA"},
    {"Id": "5418", "Name": "SLU Visa", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "SLU_VISA"},
    {"Id": "5419", "Name": "Schengen Visa", "Value": "9829", "Code": "WEB_BLS", "DepartmentOwnerUserId": "SCHENGEN_VISA"},
    {"Id": "5420", "Name": "RES VISA", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "RES_VISA"},
    {"Id": "5421", "Name": "TRA Visa", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "TRA_VISA"},
    {"Id": "5422", "Name": "RFK Visa", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "RFK_VISA"},
    {"Id": "5423", "Name": "EXT Visa", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "EXT_VISA"},
    {"Id": "5424", "Name": "ESA Visa", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "ESA_VISA"},
    {"Id": "5425", "Name": "RFI Visa", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "RFI_VISA"},
    {"Id": "5426", "Name": "LEY14 Visa", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "LEY14_VISA"},
    {"Id": "5431", "Name": "TRA Visa", "Value": "", "Code": "WEB_BLS", "DepartmentOwnerUserId": "TRA_VISA"},
    {"Id": "5432", "Name": "ESC Visa", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "ESC_VISA"},
    {"Id": "5439", "Name": "RES VISA ", "Value": "", "Code": "WEB_BLS", "DepartmentOwnerUserId": "RES_VISA"},
    {"Id": "5441", "Name": "RFK Visa", "Value": "", "Code": "WEB_BLS", "DepartmentOwnerUserId": "RFK_VISA"},
    {"Id": "5442", "Name": "SLU Visa", "Value": "", "Code": "WEB_BLS", "DepartmentOwnerUserId": "SLU_VISA"},
    {"Id": "5443", "Name": "SSU Visa", "Value": "", "Code": "WEB_BLS", "DepartmentOwnerUserId": "SSU_VISA"},
    {"Id": "5444", "Name": "EXT Visa", "Value": "", "Code": "WEB_BLS", "DepartmentOwnerUserId": "EXT_VISA"},
    {"Id": "5445", "Name": "LEY14 Visa", "Value": "", "Code": "WEB_BLS", "DepartmentOwnerUserId": "LEY14_VISA"},
    {"Id": "5446", "Name": "ESC Visa", "Value": "", "Code": "WEB_BLS", "DepartmentOwnerUserId": "ESC_VISA"},
    {"Id": "5447", "Name": "Schengen Visa", "Value": "", "Code": "WEB_BLS", "DepartmentOwnerUserId": "SCHENGEN_VISA"},
    {"Id": "5448", "Name": "RSA Visa ", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "RSA_VISA"},
    {"Id": "5449", "Name": "RLD - Embassy", "Value": "9828", "Code": "WEB_EMBASSY", "DepartmentOwnerUserId": "RLD_VISA"},
    {"Id": "5450", "Name": "TRP - Embassy", "Value": "9828", "Code": "WEB_EMBASSY", "DepartmentOwnerUserId": "TRT_VISA"},
    {"Id": "5452", "Name": "RIV Visa ", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "RIV_VISA"},
    {"Id": "5453", "Name": "PFK Visa ", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "PFK_VISA"},
    {"Id": "5454", "Name": "TRE Visa", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "TRE_VISA"},
    {"Id": "5455", "Name": "TAC Visa", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "TAC_VISA"},
    {"Id": "5456", "Name": "RIN Visa", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "RIN_VISA"},
    {"Id": "5457", "Name": "TTI Visa", "Value": "9828", "Code": "WEB_BLS", "DepartmentOwnerUserId": "TTI_VISA"},
]
missionData = [
    {
        "Id": "d133459a-6482-45ed-bd00-5ff32aa8b71b",
        "Name": "Consulate - Beijing",
        "Code": "CONSULATE_BEIJING",
    },
    {
        "Id": "235b19fd-9fce-438f-be0a-18275fd0b64d",
        "Name": "Consulate-Shanghai",
        "Code": "CONSULATE_SHANGHAI",
    },
    {
        "Id": "3ee1ef97-553a-4f8a-89c3-025cfc38e91b",
        "Name": "Consulate-Guangzhou",
        "Code": "CONSULATE_GUANGZHOU",
    },
]
applicantsNoData = [
    {"Name": "2 Members", "Value": "2"},
    {"Name": "3 Members", "Value": "3"},
    {"Name": "4 Members", "Value": "4"},
    {"Name": "5 Members", "Value": "5"},
]
visaIdData = [
    {
        "Id": "3033c6d3-579b-47e1-9602-91368d63025c",
        "Name": "National Visa",
        "VisaTypeCode": "NATIONAL_VISA",
        "AppointmentSource": "WEB_BLS",
    },
    {
        "Id": "099a0161-b428-4a10-bb1e-639b7dee4fa0",
        "Name": "Schengen Visa",
        "VisaTypeCode": "SCHENGEN_VISA",
        "AppointmentSource": "WEB_BLS",
    },
]
categoryData = [
    {"Id": "7813", "Name": "Normal", "Code": "CATEGORY_NORMAL", "LegalEntityId": "60d2df036755e8de168d8db7"},
    {"Id": "7814", "Name": "Premium", "Code": "CATEGORY_PREMIUM", "LegalEntityId": "60d2df036755e8de168d8db7"},
    {"Id": "7815", "Name": "Prime Time", "Code": "PRIME_TIME", "LegalEntityId": "8d780684-1524-4bda-b138-7c71a8591944"},
]


# 只保留每组中 z-index 最大的元素
def keep_max_zindex(elements):
    max_zindex_elements = {}

    for input_type, elems in elements.items():
        max_zindex_elem = None
        max_zindex = -float("inf")

        for elem in elems:
            zindex = elem["styles"].get("z-index", "auto")
            if zindex == "auto":
                zindex = 0
            else:
                zindex = int(zindex)

            if zindex > max_zindex:
                max_zindex = zindex
                max_zindex_elem = elem

        if max_zindex_elem:
            max_zindex_elements[input_type] = max_zindex_elem

    return max_zindex_elements


# 分组函数：根据 left 和 top 值分组
def group_by_type(elements, element_id=""):
    grouped = defaultdict(list)
    for element_id, details in elements.items():
        styles = details["styles"]
        grouped[element_id].append(
            {
                "id": element_id,
                "classes": details["classes"],
                "styles": styles,
            }
        )
    return grouped


def bypass_appointment_verify(soup: BeautifulSoup, uri="/CHN/Appointment/VisaType"):
    # 从 HTML 中提取所有 CSS 样式
    style_rules = ""
    for style in soup.find_all("style"):
        style_rules += style.string if style.string else ""

    # 从 HTML 中找出对应 ID 的元素、class 和样式
    id_to_class_and_style = {}
    response_data = {}
    el_forms = soup.select("form")
    divs_ = None
    for form in el_forms:
        if uri not in form.attrs.get("action"):
            continue
        uri = form.attrs.get("action")
        divs = form.find_all("div")
        for d in divs:
            if d.find("div"):
                divs_ = d
                break
    for el in divs_.find_all("div"):
        # el_id = el.attrs.get("id", None)
        label = el.find("label")
        label_id = label.attrs.get("for", None) if label else None
        # print(el_id, label_id)
        if not label_id:
            continue
        text_label = label.text.replace(" ", "").replace("\n", "")
        if text_label in ["AppointmentCategory*", "VisaType*", "Location*", "VisaSubType*", "Mission*"]:
            response_data[label_id] = ""
            # print(text_label)

        classes = " ".join(el.get("class", [])).split()
        # el_type = el.attrs.get("type")
        styles = {}
        for cls_ in classes:
            pattern = re.compile(rf"\.{re.escape(cls_)}\s*\{{(.*?)\}}", re.DOTALL)
            matches = pattern.findall(style_rules)

            # 合并样式
            for match in matches:
                for declaration in match.split(";"):
                    if declaration.strip():
                        property_name, _, value = declaration.partition(":")
                        styles[property_name.strip()] = value.strip()

        # 保存class和解析出来的styles
        id_to_class_and_style[label_id] = {
            "classes": classes,
            "styles": styles,  # 初始化为空，稍后将填充样式
            "id": label_id,
            "text": label.text,
        }

    # 判断元素的显示状态
    visible_ids = set()
    for element_id, details in id_to_class_and_style.items():
        styles = details["styles"]
        # 根据最终样式判断 display 属性
        display = styles.get("display", "inline")
        # style 后生效
        if "inline" in display or "block" in display:
            visible_ids.add((element_id, details["text"]))

    visible_ids = list(visible_ids)
    location_key = None
    visa_type_key = None
    visa_sub_key = None
    category_key = None
    for keymap in visible_ids:
        if "Visa Sub Type".upper() in keymap[1].upper():
            visa_sub_key = keymap[0]
        elif "Visa Type".upper() in keymap[1].upper():
            visa_type_key = keymap[0]
        elif "Category".upper() in keymap[1].upper():
            category_key = keymap[0]
        elif "Location".upper() in keymap[1].upper():
            location_key = keymap[0]
    # Appointment For 对应的key，需要去掉前两位字符
    app_for = {x.get("name")[2:]: "" for x in soup.select("input") if x.get("value") == "Individual"}
    response_data.update(app_for)
    # logger.debug(f"visa_type:{visa_type}, visa_sub_type:{visa_sub_type}, location:{location}, appointment:{appointment}")
    return visa_type_key, visa_sub_key, location_key, category_key, response_data, uri


def get_response_data(opration_items):
    timestamp_s = time.time() - 8 * 3600 - 5  # 提前5s
    time_interval = random.randint(800, 1200)
    timestamp_e = timestamp_s + time_interval / 1000.0
    res = []
    for idd in opration_items:
        date_time_obj = datetime.fromtimestamp(timestamp_s)
        iso_date_str_start = date_time_obj.isoformat(timespec="milliseconds")
        date_time_obj2 = datetime.fromtimestamp(timestamp_e)
        iso_date_str_end = date_time_obj2.isoformat(timespec="milliseconds")
        r = {"Id": idd, "Start": f"{iso_date_str_start}Z", "End": f"{iso_date_str_end}Z", "Total": int(time_interval), "Selected": True}
        res.append(r)

        timestamp_s = timestamp_e + time_interval / 500.0
        time_interval = random.randint(800, 1200)
        timestamp_e = timestamp_s + time_interval / 1000.0
    return res


def extract_variable_data(script_element, variable, is_dict=False):
    try:
        # 使用正则表达式提取JavaScript对象数组
        if is_dict:
            # soup = BeautifulSoup(script_element, "html.parser")
            # script_element = soup.find("script")
            # match2 = re.search(r'var\s*availDates\s*=\s*(.*?);', script_element, re.DOTALL)
            match = re.search(f"var\s*{variable}" + r"\s*=\s*({.*?});", script_element, re.DOTALL)
        else:
            match = re.search(f"var\s*{variable}\s*=\s*(\[.*?\]);", script_element.string, re.DOTALL)

        if match:
            json_string = match.group(1)
            json_string = json_string.replace("\n", "").replace(" ", "").replace("Xi'an", "XIAN").replace("'", '"')
            # 2. 将没有引号的键加上双引号
            if not is_dict:
                json_string = re.sub(r"(\w+):", r'"\1":', json_string)
            json_string = json_string.replace(",}", "}").replace(",]", "]")
            # 将JSON字符串转换为Python对象
            appIdData = json.loads(json_string)
            return appIdData
        return None
    except Exception as e:
        return None


@logger.catch
def bypass_appointment_verify_pipline(html_doc, user_location, purpose="Business", dateVIP=False):
    index_flag = 0
    try:
        if purpose == "Bussiness":  # 前端类型录入错误了，这里要修改下
            purpose = "Business"
        user_location = user_location.upper()  # 转大写
        purpose = purpose.replace(" ", "").upper()  # 去空格转大写
        soup = BeautifulSoup(html_doc, "html.parser")

        script_element = soup.find("script", string=re.compile(r"var\s*locationData"))
        if not script_element:
            script_element = soup.find("script", string=re.compile(r"var\s*visasubIdData"))
        elif not script_element:
            script_element = soup.find("script", string=re.compile(r"var\s*visaIdData"))
        elif not script_element:
            script_element = soup.find("script", string=re.compile(r"var\s*categoryData"))

        locationData_from_page = extract_variable_data(script_element, "locationData")
        visasubIdData_from_page = extract_variable_data(script_element, "visasubIdData")
        visaIdData_from_page = extract_variable_data(script_element, "visaIdData")
        # applicantsNoData_from_page = extract_variable_data(script_element, "applicantsNoData")
        categoryData_from_page = extract_variable_data(script_element, "categoryData")

        if locationData_from_page and visasubIdData_from_page and visaIdData_from_page and categoryData_from_page:
            pass
        else:
            return False, (locationData_from_page, visasubIdData_from_page, visaIdData_from_page, categoryData_from_page), "visaIdData字段抽取错误", script_element.string

        # form_dict = {_.get("name"): _.get("value") for _ in soup.select("input")}
        # # 修改成个人申请
        # for k, v in form_dict.items():
        #     if v == "Family":
        #         form_dict[k] = "Individual"
        form_dict = {_.get("name"): _.get("value") for _ in soup.select("input") if _.get("value") != "Family"}
        form_dict["AppointmentFor"] = None
        # 获取对应的id及所有的ID
        visa_type_key, visa_sub_key, location_key, category_key, response_data, uri = bypass_appointment_verify(soup)

        if visa_sub_key and visa_type_key and location_key and category_key:
            pass
        else:
            return False, (visa_type_key, visa_sub_key, location_key, category_key, response_data, uri), "验证码解析发生错误", ""

        location_id = list(filter(lambda x: x.get("Code").upper() == user_location, locationData_from_page))[0]["Id"]
        index_flag = 1
        visa_type_id = list(filter(lambda x: "SCHENGENVISA" in x.get("Name", "").replace(" ", "").upper(), visaIdData_from_page))[0]["Id"]
        index_flag = 2
        visa_id_list = list(filter(lambda x: visa_type_id == x.get("Value"), visasubIdData_from_page))
        # visa_sub_id = list(filter(lambda x: purpose == x.get("Name", "").replace(" ", "").upper(), visasubIdData_from_page))[0]["Id"]
        if len(visa_id_list):
            visa_sub_id = visa_id_list[0]["Id"]
            for visa_sub in visa_id_list:
                if purpose in visa_sub["Name"].upper():
                    visa_sub_id = visa_sub["Id"]
                    break
                elif purpose in visa_sub["DepartmentOwnerUserId"].upper():
                    visa_sub_id = visa_sub["Id"]
                    break
        else:
            return False, (visa_type_key, visa_sub_key, location_key, category_key, response_data, uri), f"不存在类型 {purpose} 的预约", ""
        index_flag = 3
        openVip = False
        # 这里要通过 LegalEntityId 和 location_id匹配得到唯一的ID
        if dateVIP:
            match_one = list(filter(lambda x: "Premium" == x.get("Name") and x.get("LegalEntityId") == location_id, categoryData_from_page))
            match_two = list(filter(lambda x: "PrimeTime" == x.get("Name").replace(" ", "") and x.get("LegalEntityId") == location_id, categoryData_from_page))
            if len(match_one):
                category_id = match_one[0].get("Id")
                openVip = True
            elif len(match_two):
                category_id = match_two[0].get("Id")
                openVip = True
            else:
                category_id = list(filter(lambda x: "NORMAL" == x.get("Name").upper() and x.get("LegalEntityId") == location_id, categoryData_from_page))[0].get("Id", "")
                openVip = False
        else:
            category_id = list(filter(lambda x: "NORMAL" == x.get("Name").upper() and x.get("LegalEntityId") == location_id, categoryData_from_page))[0].get("Id", "")
            openVip = False

        if openVip:
            logger.debug("#查询VIP号#")

        index_flag = 4

        info_dict = {}
        info_dict[visa_type_key] = visa_type_id
        info_dict[visa_sub_key] = visa_sub_id
        info_dict[location_key] = location_id
        info_dict[category_key] = category_id

        response_data.update(info_dict)
        form_dict.update(info_dict)
        form_dict["ResponseData"] = json.dumps(response_data)
        return True, form_dict, openVip, uri
    except Exception as e:
        logger.error(f"验证码解析流程错误: {index_flag} {e}")
        return False, {}, "验证码解析流程错误", ""


@logger.catch
def bypass_appointment_verify_code_pipline(session, res_verify_code, send_request=True):
    verify_code_html = res_verify_code.text
    try:
        # code, verify_data, pic_number, uri = bypass_verify_code_func(verify_code_html)
        for _ in range(5):
            code, verify_data, pic_number, uri = bypass_verify_code_func(verify_code_html)
            if not code:  # TODO reload
                verify_code_page = session.post(url_host + verify_data, verify=False, timeout=15)
                logger.warning(f"#放号查询# 重新加载验证码页面{verify_code_page.status_code}:{verify_code_page.url}")
                if verify_code_page.status_code == 200:
                    verify_code_html = verify_code_page.text
                    continue  
                else:
                    return False, "验证码验证失败"

            # step3 验证码识别
            url_captcha_submit = url_host + uri
            # 不发送请求，只保存好提取的数据
            if not send_request:
                return url_captcha_submit, verify_data
            # 稳定代理IP
            res_verify = session.post(url_captcha_submit, data=verify_data, verify=False, timeout=15)
            if "/CHN/appointment/appointmentcaptcha".upper() in res_verify.url.upper():
                logger.debug(f"#放号查询# 验证码选错. Number:{pic_number} : {res_verify.status_code} : {res_verify.url}")
                verify_code_html = res_verify.text
                # code, verify_data, pic_number, uri = bypass_verify_code_func(res_verify.text)
                continue
            elif res_verify.status_code != 200:
                logger.error(f"#放号查询# 验证码验证失败。{res_verify.status_code} : {res_verify.text[-250:]}")
                return False, verify_data
            else:
                return True, res_verify
        return False, verify_data
    except Exception as e:
        logger.error(f"#预约# 验证码流程错误: {e}")
        return False, e


@logger.catch
def extract_form_data_from_html(html_doc):
    soup = BeautifulSoup(html_doc, "html.parser")
    form_data = {_.get("name"): _.get("value") for _ in soup.select("input")}
    form_data["X-Requested-With"] = "XMLHttpRequest"
    return form_data


if __name__ == "__main__":
    with open("temp.html", "r") as f:
        html_string = f.read()
    res = bypass_appointment_verify_pipline(html_string, "SHENZHEN", dateVIP=True)
    print(res[1])
