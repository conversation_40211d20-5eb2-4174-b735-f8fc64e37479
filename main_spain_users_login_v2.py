from curl_cffi import requests
import json
import time
import threading
from queue import Queue
from copy import deepcopy

from extension.session_manager import create_session
import redis

from spain_visa_login import user_login
from extension.logger import logger
from tool import get_new_proxy, get_random_user_agent
from config import headers
from user_manager import save_user_2_redis_queue, spain_user_field, spain_faker_scaning
from user_manager import redis_client, hget_redis_user, get_user_info, get_user_date_status, set_user_date_status
from main_spain_users_login_v2_optimized import main

redis_cli = redis_client.client

login_queue = Queue()

def keep_user_is_login(user_field):
    user = hget_redis_user(user_field, spain_user_field)
    if not user:
        return None
    if user.get("status") != "update_appointment":
        return user
    if get_user_date_status(user):  # 正在预约的不能重新登录
        return user

    header = deepcopy(headers)
    header["user-agent"] = get_random_user_agent()
    session = requests.Session(impersonate="chrome131")
    session.headers = header

    if not user.get("is_login", False) or int(time.time()) - int(user.get("updateTime", 0)) > 600:
        proxy = get_new_proxy(faker=False)
        if not proxy:
            return user
        proxy_dict = {"http": proxy, "https": proxy}
        session.proxies.update(proxy_dict)

        logger.debug(f"##正在登录##{user.get('email')}: {user.get('passportNO')}, update:{user.get('updateTime','')}")

        flag_login, info_login = user_login(user, session)
        if not flag_login:
            user = get_user_info(user)
            user["is_login"] = False
            logger.error(f"##用户登录失败## {user.get('chnname')} - {user.get('centerCode')} - {user.get('visaTypeCode')}- {user.get('passportNO')} -{user.get('startDate')} - {user.get('endDate')} - {user.get('email')} ")
        else:
            logger.success(f"##用户登录成功## {user.get('chnname')} - {user.get('centerCode')} - {user.get('visaTypeCode')} - {user.get('passportNO')} -{user.get('startDate')} - {user.get('endDate')} - {user.get('email')}")
            cookie_dict = session.cookies.get_dict()
            # password = info_login.get('password')
            user = get_user_info(user)
            # user['password'] = password
            user["dest"] = "0cf9da11-cb76-4566-81e5-8628d5488e3c"  # 目的地
            user["cookies"] = cookie_dict
            user["proxy"] = proxy_dict["http"]
            user["is_login"] = True
            user["updateTime"] = int(time.time())
        # 失败成功都要更新到redis
        set_user_date_status(user, False)
        save_user_2_redis_queue(user)
    else:
        pass
        # logger.debug(f"##保持登录##user:{user.get('email')}, login:{user.get('is_login')}, update:{user.get('updateTime')}")
    return user

def work_scan_appointment_only():
    while True:
        if login_queue.empty():
            time.sleep(2)
            continue
        else:
            user_field = login_queue.get()
            user_exist = keep_user_is_login(user_field)
            if user_exist:
                login_queue.put(user_field)
            # else:
            #     in_queue_users_field.discard(user_field)
            time.sleep(0.2)
            login_queue.task_done()


def monitor_users():
    in_queue_users_field = set()
    while True:
        try:
            # 获取当前所有用户
            all_users_field = set(redis_cli.hkeys(spain_user_field))
            # 找出新增或状态变化的用户
            new_users = all_users_field - in_queue_users_field

            # 将需要处理的用户加入队列
            for user_field in new_users:
                login_queue.put(user_field)
                in_queue_users_field.add(user_field)

            time.sleep(1)  # 适当休眠，避免CPU占用过高

        except redis.exceptions.WatchError:
            # 处理乐观锁冲突
            continue
        except Exception as e:
            print(f"Monitor error: {e}")
            time.sleep(2)


if __name__ == "__main__":
    main()
    
    # threading.Thread(target=monitor_users, daemon=True).start()
    # for _ in range(15):
    #     thread = threading.Thread(target=work_scan_appointment_only, daemon=True)
    #     thread.start()

    # while True:
    #     logger.info("登录脚本正常运行中...")
    #     time.sleep(600)
