# import requests
import sys
import time
import threading
import json
from queue import Queue
from copy import deepcopy
from datetime import datetime
from curl_cffi import requests
from spain_visa_login import user_login
from extension.logger import logger
from tool import get_new_proxy, get_random_user_agent, str_2_timestamp
from config import headers
from user_manager import save_user_2_redis_queue, get_users_with_queue_name, subscribe_redis_msg, spain_user_field
from user_manager import get_user_info, get_user_date_status, set_user_date_status

user_queue = Queue()
# spain_user_field = "spainUserDatas"


# @timmer
def keep_user_is_login(user):
    user = get_user_info(user)  # 刷新下redis的用户信息
    if not user:
        return
    if get_user_date_status(user):  # 正在预约的不能重新登录
        return

    header = deepcopy(headers)
    header["user-agent"] = get_random_user_agent()
    session = requests.Session(impersonate="chrome131")
    session.headers = header

    if not user.get("is_login", False) or int(time.time()) - int(user.get("updateTime", 0)) > 600:
        proxy = get_new_proxy(faker=False)
        if not proxy:
            return
        proxy_dict = {"http": proxy, "https": proxy}
        session.proxies.update(proxy_dict)

        logger.debug(f"##正在登录##{user.get('email')}: {user.get('passportNO')}, update:{user.get('updateTime','')}")

        flag_login, info_login = user_login(user, session)
        if not flag_login:
            user = get_user_info(user)
            user["is_login"] = False
            logger.error(f"##用户登录失败## {user.get('chnname')} - {user.get('centerCode')} - {user.get('visaTypeCode')} - {user.get('email')} - {user.get('passportNO')} -{user.get('startDate')} - {user.get('endDate')}")
        else:
            logger.success(f"##用户登录成功## {user.get('chnname')} - {user.get('centerCode')} - {user.get('visaTypeCode')} - {user.get('email')} - {user.get('passportNO')} -{user.get('startDate')} - {user.get('endDate')}")
            cookie_dict = session.cookies.get_dict()
            # password = info_login.get('password')
            user = get_user_info(user)
            # user['password'] = password
            user["dest"] = "0cf9da11-cb76-4566-81e5-8628d5488e3c"  # 目的地
            user["cookies"] = cookie_dict
            user["proxy"] = proxy_dict["http"]
            user["is_login"] = True
            user["updateTime"] = int(time.time())
        # 失败成功都要更新到redis
        set_user_date_status(user, False)
        save_user_2_redis_queue(user)
    else:
        pass
        # logger.debug(f"##保持登录##user:{user.get('email')}, login:{user.get('is_login')}, update:{user.get('updateTime')}")
    return user, session


def worker_keep_user_login():
    while not user_queue.empty():
        user = user_queue.get()
        keep_user_is_login(user)
        user_queue.task_done()
        time.sleep(0.5)


def spain_users_login(thread_count=10):
    logger.info("##预约# 开始登录用户...")
    while True:
        all_users = get_users_with_queue_name("brazil_faker_app_users")
        users = list(filter(lambda u: u.get("status") == "update_appointment", all_users))
        # 未登录的用户添加到队列进行登陆
        if len(users) <= 0:
            time.sleep(0.5)
            continue

        for user in users:
            user_queue.put(user)

        threads = []
        for _ in range(thread_count):
            thread = threading.Thread(target=worker_keep_user_login, daemon=True)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        time.sleep(0.5)


if __name__ == "__main__":
    spain_users_login()

#####################################


def date_avaliable(user_info, all_dates):
    start_date = user_info.get("startDate", "").replace("/", "-")
    end_date = user_info.get("endDate", "").replace("/", "-")
    # 判断出是否有可预约日期
    date_avaliable_flag = False
    for date_item in all_dates:
        if str_2_timestamp(start_date) <= str_2_timestamp(date_item) <= str_2_timestamp(end_date):
            date_avaliable_flag = True
            break
    return date_avaliable_flag


def start_login_spain_users(channel, msg_str):
    msg_json = json.loads(msg_str)
    center_code = msg_json.get("centerCode")  # 开发地区
    open_flag = msg_json.get("open")  # 是否是开发
    open_days = msg_json.get("dates")  # 开放日期数组

    # 已经开始了就不接受redis消息了
    logger.info(f"##预约#收到redis放号通知:{channel}, 开始登录 {center_code} 地区用户")
    thread_count = 5
    login_start_time = int(time.time())
    while True:
        # 保持110s，超过时间还有没预约的就不预约了
        if int(time.time()) - login_start_time > 60 * 3 - 10:
            break
        all_users = get_users_with_queue_name(spain_user_field)
        # 筛选等待预约的客户
        users_wating_appointment = list(filter(lambda u: u["status"] == "update_appointment" and u.get("centerCode") == center_code, all_users))
        users = users_wating_appointment
        if len(users) <= 0:
            break

        for user in users:
            if date_avaliable(user, open_days):
                user_queue.put(user)

        if user_queue.empty():
            logger.debug(f"##预约#{center_code} 地区无用户期望的日期")
            time.sleep(1)
            continue

        work_start = time.time()
        threads = []
        # 使用 for 循环创建多线程并传入参数
        for _ in range(thread_count):
            thread = threading.Thread(target=worker_keep_user_login, daemon=True)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        work_finish = time.time()
        t_interval = int(work_finish - work_start)
        logger.info(f"##预约## {center_code} 完成{len(users)}个用户的token刷新,耗时：{t_interval} s")
        time.sleep(1)


def listen_redis_pub():
    logger.debug("开启redis sub 订阅")
    subscribe_redis_msg(start_login_spain_users)
    while True:
        logger.debug("redis_sub 订阅中...")
        time.sleep(60 * 10)
