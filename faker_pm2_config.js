module.exports = {
    apps: [
      {
        name: "spain_faker_login",
        script: "python3",
        args: "main_spain_faker_users_login.py 15",
        cwd: "/root/visa/SpainVisa",
        interpreter: "/root/visa/conda_env/bin/python",
        out_file: "./logs_today/faker_login.log",
        error_file: "./logs_today/faker_login.log",
        merge_logs: true,
        env: {
          PATH: "/root/visa/conda_env/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin",
        },
        wait_ready: true,
        listen_timeout: 10000,
      },
      {
        name: "spain_faker_appointment",
        script: "python3",
        args: "main_spain_faker_users_appointment_v2.py 2",
        cwd: "/root/visa/SpainVisa",
        interpreter: "/root/visa/conda_env/bin/python",
        out_file: "./logs_today/faker_scaning.log",
        error_file: "./logs_today/faker_scaning.log",
        merge_logs: true,
        env: {
          PATH: "/root/visa/conda_env/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin",
        },
        wait_ready: true,
        listen_timeout: 10000,
      },
    ],
  };
