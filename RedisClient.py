# -*- coding: utf-8 -*-
import redis
import json
import threading
import time
# from extension.logger import logger


# *************
# *************
class RedisClient:
    def __init__(self, host="*************", port=6379, password="TicketsCache#2023", db=0):
        self.client = redis.Redis(
            host=host,
            port=port,
            password=password,
            db=db,
            decode_responses=True,
            retry_on_timeout=True,
        )
        self.host = host

    def set(self, key, value, expire_in_seconds=None):
        if expire_in_seconds:
            return self.client.setex(key, expire_in_seconds, value)
        return self.client.set(key, value)

    def get(self, key):
        return self.client.get(key)

    def delete(self, key):
        return self.client.delete(key)

    def hset(self, hash_name, field, value):
        return self.client.hset(hash_name, field, value)

    def hget(self, hash_name, field):
        return self.client.hget(hash_name, field)

    def hgetall(self, hash_name):
        data = self.client.hgetall(hash_name)
        json_array = []
        for field, value in data.items():
            try:
                json_object = json.loads(value)
                json_array.append(json_object)
            except json.JSONDecodeError:
                pass  # 如果解析失败，则跳过这个值
        return json_array

    def hdel(self, hash_name, *fields):
        try:
            return self.client.hdel(hash_name, *fields)
        except:
            return None

    def expire(self, key, seconds):
        return self.client.expire(key, seconds)

    def quit(self):
        self.client.close()

    def __init_subscriber(self):
        self.subscriber = redis.Redis(
            host=self.host,
            port=6379,
            db=0,
            password="TicketsCache#2023",
            decode_responses=True,
            retry_on_timeout=True,
        )
        self._pubsub = self.subscriber.pubsub()

    def subscribe(self, channel, callback):
        if not hasattr(self, "subscriber"):
            self.__init_subscriber()

        def listen():
            self._pubsub.subscribe(channel)
            for message in self._pubsub.listen():
                if message["type"] == "message":
                    callback(message["channel"], message["data"])

        thread = threading.Thread(target=listen)
        thread.daemon = True
        thread.start()
        
    def subscribe_sync(self, channel):
        if not hasattr(self, "subscriber"):
            self.__init_subscriber()

        self._pubsub.subscribe(channel)
        for message in self._pubsub.listen():
            if message["type"] == "message":
                return message["data"]

        
    def unsubscribe(self, channel):
        if hasattr(self, "subscriber"):
            self._pubsub.unsubscribe(channel)

    def __init_publisher(self):
        self.publisher = redis.Redis(
            host=self.host,
            port=6379,
            db=0,
            password="TicketsCache#2023",
            decode_responses=True,
            retry_on_timeout=True,
        )

    def publish(self, channel, message):
        if not hasattr(self, "publisher"):
            self.__init_publisher()
        self.publisher.publish(channel, message)

    def get_email_otp(self, email):
        while True:
            # 从redis获取邮箱的默认密码
            time.sleep(1)
            default_pwd = self.get(email)
            if default_pwd:
                print(f"邮箱:{email}\n确认码:{default_pwd}")
                # email_client.delete(email)
                break
        return default_pwd


if __name__ == "__main__":
    client = RedisClient(host="*************")
    res = client.hgetall("spain_users_scaning")
    with open("all_users.json", "w", encoding="utf8") as f:
        json.dump(res, f, ensure_ascii=False)
    print(res)
