# import json
import requests
from config import url_host
from extension.logger import logger
from bs4 import BeautifulSoup
import os
from bypass_book_appointment_verify import bypass_appointment_verify_pipline, bypass_appointment_verify_code_pipline, extract_variable_data
from tool import get_random_user_agent, send_dd_msg, extract_alert_msg
from spain_visa_update_profile import update_user_profile, confirm_email
from user_manager import spain_faker_scaning

# 预约签证
url_visa_newappointment = url_host + "/CHN/appointment/newappointment"

session_d = requests.session()


def choose_visa_type(session, user_info, res_visa_type):
    user_location = user_info.get("centerCode", "SHANGHAI").upper()  # 面谈地
    purpose = user_info.get("visaTypeCode", "Tourism")  # 签证目的
    # 成都地区的 旅游和商务 都用短期替代
    # if user_location == "CHENGDU":
    #     purpose = "Business"
    dateVIP = user_info.get("dateVIP", False)
    flag, form_dict, openVIP, uri_submit = bypass_appointment_verify_pipline(res_visa_type.text, user_location, purpose, dateVIP)
    if not flag:
        logger.error(f"#放号查询#表单字段提取失败:{user_info.get('email')}:{user_info.get('passportNO')}:{openVIP}:{form_dict}:{uri_submit}")
        return False, None, ""
    # 修改用户状态 是否扫了VIP号
    user_info["dateVIP"] = openVIP
    url_post_visatype = url_host + uri_submit
    res = session.post(url_post_visatype, data=form_dict, verify=False, timeout=15)
    if res.status_code != 200:
        logger.debug(f"VisaType:{res.status_code}:{res.url.split('?')[0]}")
        return False, res, res.url.split("?")[0]
    return True, res, res.url.split("?")[0]


def book_appointment(user_info, session=session_d):
    try:
        user_log_info = f"{user_info.get('centerCode')}-{user_info.get('visaTypeCode')}-{user_info.get('email')}"
        res_visa_type = session.get(url_visa_newappointment, verify=False, timeout=30)
        if res_visa_type.status_code != 200:
            logger.error(f"#放号查询#选签证类型前验证错误。{user_log_info}:{res_visa_type.status_code}: {res_visa_type.text[-200:-100]}")
            return False, res_visa_type.status_code

        # 如果重定向到了选签证信息页面
        res_slot_selection = None
        if "/CHN/Appointment/VisaType".upper() in res_visa_type.url.upper():
            flag_, res_verify_page, visa_direct_url = choose_visa_type(session, user_info, res_visa_type)
            if not flag_:
                logger.error(f"#放号查询#Appointment_VisaType error:{res_verify_page}:{visa_direct_url}")
                return False, res_visa_type
            # 这里直接进入了选类型的页面，跳过了验证码页面
            if "/CHN/Appointment/NewAppointment".upper() in visa_direct_url.upper():
                res_slot_selection = res_verify_page
                msg = extract_alert_msg(res_verify_page.text)
                if user_info["queue_name"] == spain_faker_scaning and "You have not filled out and completed the applicant detail form" in msg:
                    flag_profile, res_profile = update_user_profile(user_info, session)
                    if not flag_profile:
                        logger.error(f"更新失败:{user_info.get('centerCode')} {user_info.get('email')}")
                    else:
                        logger.success(f"更新成功:{user_info.get('centerCode')} {user_info.get('email')}")
                # logger.error(f"#放号查询#/CHN/Appointment/NewAppointment error:{msg}")
                return True, {"success": False, "message": msg}
            # 先选类型再验证码, 跳转到了验证码页面 /Appointment/AppointmentCaptcha
            elif "/CHN/Appointment/AppointmentCaptcha".upper() in visa_direct_url.upper():
                flag_verify, verify_res = bypass_appointment_verify_code_pipline(session, res_verify_page)
                if not flag_verify:
                    logger.error(f"#放号查询#Appointment_Captcha error: {verify_res}")
                    return False, "Appointment_Captcha error"
                # 重定向成功了
                if "/CHN/Appointment/SlotSelection".upper() in verify_res.url.upper():
                    res_slot_selection = verify_res
                    # visa_direct_url = verify_res.url
                else:  # 没有放号
                    msg = extract_alert_msg(verify_res.text)
                    if not msg:
                        msg = f"NO slots available {verify_res.url.split('?')[0]}"
                    else:
                        msg = f"{msg}"
                    logger.debug(f"#放号查询#Captcha SlotSelection error: {verify_res.status_code}:{verify_res.url}")
                    return True, {"success": False, "message": msg}
            elif "/CHN/Appointment/SlotSelection".upper() in visa_direct_url.upper():
                res_slot_selection = res_verify_page
            else:
                logger.error(f"#放号查询# VisaType error:{res_verify_page}:{visa_direct_url}")
                msg = extract_alert_msg(res_verify_page.text)
                if not msg:
                    msg = f"NO slots available {res_verify_page.url.split('?')[0]}"
                else:
                    msg = f"{msg}"
                logger.debug(f"#放号查询#Captcha SlotSelection error: {res_verify_page.status_code}:{res_verify_page.url}")
                return False, res_verify_page
        # 重定向到图片验证码页面
        elif "/CHN/Appointment/AppointmentCaptcha".upper() in res_visa_type.url.upper():
            flag_verify, verify_res = bypass_appointment_verify_code_pipline(session, res_visa_type)
            if not flag_verify:
                logger.debug(f"#放号查询#Appointment_Captcha error: {verify_res}")
                return False, "验证码验证失败"
            # 提交选择会直接重定向到选日期，否则就是没有可预约日期
            flag_, res_verify_page, visa_direct_url = choose_visa_type(session, user_info, verify_res)
            if flag_:
                # 重定向成功了 查到开放日期了
                if "/CHN/Appointment/SlotSelection".upper() in visa_direct_url.upper():
                    res_slot_selection = res_verify_page
                else:
                    msg = extract_alert_msg(res_verify_page.text)
                    if not msg:
                        msg = f"NO slots available  {visa_direct_url.split('?')[0]}"
                    else:
                        msg = f" {msg}"
                    if user_info["queue_name"] == spain_faker_scaning and "You have not filled out and completed the applicant detail form" in msg:
                        flag_profile, res_profile = update_user_profile(user_info, session)
                        if not flag_profile:
                            logger.error(f"更新失败:{user_info.get('centerCode')} {user_info.get('email')}")
                        else:
                            logger.success(f"更新成功:{user_info.get('centerCode')} {user_info.get('email')}")
                    logger.debug(f"#放号查询#VisaType SlotSelection error: {res_verify_page.status_code}:{res_verify_page.url}")
                    return True, {"success": False, "message": msg}
            else:
                logger.error(f"#放号查询#visa_type error: {res_verify_page}:{visa_direct_url}")
                return False, res_verify_page.status_code
        else:  # 可能重新登录了
            alert_msg = user_log_info
            # 解析并打印下错误提示
            alert_text = extract_alert_msg(res_visa_type.text)
            if alert_text:
                alert_msg = alert_msg + " Tips: " + alert_text[:100]
                # logger.warning(f"#放号查询# newappointment请求错误: {alert_msg}")  # {res_visa_type.text[:200]}
            logger.warning(f"#放号查询#重定向到URL:{res_visa_type.url}:{alert_msg}")
            # 邮箱双重确认
            if "/CHN/appointment/DataProtectionEmailSent".upper() in res_visa_type.url.upper():
                confirm_flag, res_ = confirm_email(user_info, session)
                logger.success(f"#放号查询# 邮箱确认{'成功' if confirm_flag else '失败'}:{user_log_info}")
            return False, f"#放号查询#重定向到URL:{res_visa_type.url}"

        availDates = extract_variable_data(res_slot_selection.text, "availDates", is_dict=True)
        if availDates:
            open_days = list(filter(lambda x: x.get("SingleSlotAvailable", False), availDates.get("ad", [])))
            open_day_arr = [e["DateText"] for e in open_days]
            return True, {"success": True, "html": res_slot_selection.text, "days": open_day_arr}
        else:
            logger.error(f"#放号查询#查询失败.{user_info.get('email')}:{user_info.get('passportNO')}:{res_slot_selection.status_code}:{res_slot_selection.text[-200:]}")
            return True, {"success": False, "message": res_slot_selection.text}

    except Exception as e:
        logger.debug(f"#放号查询#查询流程出错: {user_info['email']}-{user_info.get('passportNO')}-{user_info['centerCode']}-{user_info.get('visaTypeCode')}, error:{e}")
        return False, e.args[0]


if __name__ == "__main__":
    print("end")
