import re
from bs4 import BeautifulSoup

import json
import time
from extension.logger import logger
from datetime import datetime
from config import genderData, journeyPurposeData, maritalStatusData
from tool import str_2_timestamp

from bypass_book_appointment_verify import locationData

# 预约
url_visa_type_verify = "https://spain.blscn.cn/CHN/bls/visatypeverification"
url_host = "https://spain.blscn.cn"

url_available_date = "/CHN/appointment/GetAvailableAppointmentDates"
url_available_slot = "/CHN/appointment/GetAvailableSlotsByDate"


def bypass_date_appointment_verify(soup: BeautifulSoup, uri="/CHN/Appointment/SlotSelection"):
    # 从 HTML 中提取所有 CSS 样式
    style_rules = ""
    for style in soup.find_all("style"):
        style_rules += style.string if style.string else ""

    # 从 HTML 中找出对应 ID 的元素、class 和样式
    id_to_class_and_style = {}
    el_forms = soup.select("form")
    divs_ = None
    for form in el_forms:
        if uri not in form.attrs.get("action"):
            continue
        uri = form.attrs.get("action")
        divs = form.find_all("div")
        for d in divs:
            if d.find("div"):
                divs_ = d
                break
    for el in divs_.find_all("div"):
        # el_id = el.attrs.get("id", None)
        label = el.find("label")
        label_id = label.attrs.get("for", None) if label else None
        # print(el_id, label_id)
        if not label_id:
            continue
        classes = " ".join(el.get("class", [])).split()
        # el_type = el.attrs.get("type")
        styles = {}
        for cls_ in classes:
            pattern = re.compile(rf"\.{re.escape(cls_)}\s*\{{(.*?)\}}", re.DOTALL)
            matches = pattern.findall(style_rules)

            # 合并样式
            for match in matches:
                for declaration in match.split(";"):
                    if declaration.strip():
                        property_name, _, value = declaration.partition(":")
                        styles[property_name.strip()] = value.strip()

        # 保存class和解析出来的styles
        id_to_class_and_style[label_id] = {
            "classes": classes,
            "styles": styles,  # 初始化为空，稍后将填充样式
            "id": label_id,
            "text": label.text,
        }

    # 判断元素的显示状态
    visible_ids = set()
    for element_id, details in id_to_class_and_style.items():
        styles = details["styles"]
        # 根据最终样式判断 display 属性
        display = styles.get("display", "inline")
        # style 后生效
        if "inline" in display or "block" in display:
            visible_ids.add((element_id, details["text"]))

    visible_ids = list(visible_ids)
    slot_key = None
    day_key = None
    for keymap in visible_ids:
        if "slot".upper() in keymap[1].upper():
            slot_key = keymap[0]
        elif "date".upper() in keymap[1].upper():
            day_key = keymap[0]

    # logger.debug(f"visa_type:{visa_type}, visa_sub_type:{visa_sub_type}, location:{location}, appointment:{appointment}")
    return day_key, slot_key, {x: "" for x in id_to_class_and_style.keys()}, uri


def extract_slot_time_link(html_string):
    uri_pattern = re.compile(r'"/CHN/appointment/GetAvailableSlotsByDate\s*([^"]+)"')
    match = uri_pattern.search(html_string)
    link = None
    if match:
        link = match.group(1)
        link = "/CHN/appointment/GetAvailableSlotsByDate" + link
    return link


def extract_func_params(html_string, method_name="LoadAppointmentDates"):
    # 使用正则表达式匹配方法调用中的参数
    pattern = r"OnApplicantSelect\('([^']*)',\s*'([^']*)'\)"
    match1 = re.search(pattern, html_string)
    if match1:
        params_1 = match1.group(1)
        params_2 = match1.group(2)
        # logger.debug(f"params_1:{params_1}, params_2:{params_2}")
        return params_1, params_2
    else:
        return None, None


def pick_available_day(user_info, open_dates):
    start_date = user_info.get("startDate", "").replace("/", "-")
    end_date = user_info.get("endDate", "").replace("/", "-")
    stamp_start = str_2_timestamp(start_date)
    stamp_end = str_2_timestamp(end_date)

    # 筛选出可预约日期
    available_dates = []
    # open_dates = list(filter(lambda x: x.get("SingleSlotAvailable", False), all_dates))
    for date_item in open_dates:
        item_stamp = str_2_timestamp(date_item)
        # 初步筛选在期望日期范围内
        if item_stamp >= stamp_start and item_stamp <= stamp_end:
            if int(user_info.get("acceptND", 2)) == 2:
                # 不接受隔天号，可约日期要距离当天大于24小时
                if item_stamp - time.time() > 3600 * 24:  # datetime.now().timestamp()
                    available_dates.append(date_item)
                else:
                    logger.warning(f"隔天号{date_item}丢弃：{user_info.get('chnname')}-{user_info.get('centerCode')}-{user_info.get('visaTypeCode')}-{user_info.get('dateVIP')}")
            else:
                available_dates.append(date_item)
    return available_dates


def pick_available_time(all_times):
    # 筛选出第一个可预约日期
    open_times = list(filter(lambda x: x.get("Count", 0) == 1, all_times))
    if open_times:
        return open_times[0].get("Name")
    return False


AppointmentCategoryIdData = []


def choose_avalilable_dates(user_info, session, page_form={}):
    try:
        # 选择签发地
        user_location = user_info.get("centerCode", "SHANGHAI").upper()
        loation_item = list(filter(lambda x: x.get("Code") == user_location, locationData))
        location_id = loation_item[0].get("Id")
        location_id = page_form.get("LocationId", location_id)
        # appointment category
        # 这里其实只缺少 missionId 字段，其他字段页面都带出来了
        appointment_items = list(filter(lambda x: x.get("LegalEntityId") == location_id, AppointmentCategoryIdData))
        appointment_item = appointment_items[0]
        appointment_id = appointment_item.get("Id")
        mission_id = appointment_item.get("MissionId")
        form_dict = {
            "locationId": page_form.get("LocationId", location_id),
            "categoryId": page_form.get("AppointmentCategoryId", appointment_id),
            "visaType": page_form.get("VisaType", "099a0161-b428-4a10-bb1e-639b7dee4fa0"),  # 申根签证
            "visaSubType": page_form.get("VisaSubTypeId", "6a7bbf0d-217c-4bc1-a458-54f60bff4811"),  # 申根签证
            "applicantCount": page_form.get("ApplicantsNo", 1),
            "dataSource": page_form.get("DataSource", "WEB_BLS"),
            "missionId": mission_id,  # 领事馆 - 上海
        }
        url_date = url_host + url_available_date
        # 获取可预约的日期
        res_date = session.post(url_date, params=form_dict, verify=False, timeout=15)
        if res_date.status_code != 200:
            logger.error(f"获取预约日期出错：{res_date.status_code}")
            return False, None, None

        # 筛选符合用户期望的日期
        available_days = pick_available_day(user_info, res_date.json().get("ad", []))
        if len(available_days) == 0:
            logger.warning("暂无用户期望的可预约日期")
            return False, None, None

        session.headers.update({"referer": url_date})
        url_date_slot = url_host + url_available_slot
        for pick_day in available_days:
            form_dict["appointmentDate"] = pick_day
            # 获取全部时间段
            res_time = session.post(url_date_slot, params=form_dict, verify=False, timeout=15)
            if res_time.status_code != 200:
                logger.error(f"获取预约日期出错：{res_time.status_code}")
                return False, None, None
            # 可预约的时间段 eg: 8:30 - 9:00
            pick_time = pick_available_time(res_time.json())
            if not pick_time:
                # 当天没有就看下一个可预约日期
                continue
            else:
                form_dict.pop("appointmentDate")
                return form_dict, pick_day, pick_time
        return False, None, None
    except Exception as e:
        logger.error(f"选签证日期错误{e.args[0]}")
        return False, None, None


def pick_email_code_send_url(html_string):
    uri_pattern = re.compile(r'"/CHN/blsappointment/SendAppointmentVerificationCode\s*([^"]+)"')
    match = uri_pattern.search(html_string)
    if match:
        link = match.group(1)
        link = "/CHN/blsappointment/SendAppointmentVerificationCode" + link
        return link
    return None


def extract_date_slot_id(html_string, method_name="LoadAppointmentDates"):
    # 使用正则表达式匹配方法调用中的参数
    pattern = rf"{method_name}\('([^']*)',\s*'([^']*)'\)"
    match1 = re.search(pattern, html_string)
    if match1:
        day_el_id = match1.group(1)
        time_el_id = match1.group(2)
        logger.debug(f"dayId:{day_el_id}, timeId:{time_el_id}")
        return day_el_id, time_el_id
    else:
        return None, None


def extract_date_verify_code_link(html_doc):
    try:
        uri_pattern = re.compile(r"win\.iframeOpenUrl\s*=\s*'([^']*)'")
        match = uri_pattern.search(html_doc)
        link = match.group(1) if match else None
        return link
    except Exception as e:
        return None


def get_appointment_detail_info(html_string, account, pid):
    soup = BeautifulSoup(html_string, "html.parser")
    form_visa = soup.find("form")
    form_visa_dict = {_.get("id"): _.get("value") for _ in form_visa.select("input")}
    app_id = form_visa_dict.get("ApplicantId_0", "")
    try:
        data = extract_appointment_detail_dict(html_string)
        if data:
            default_dict = {
                "PassportNo": account.get("passportNO"),
                "ParentId": pid,
                "ApplicantId": app_id,
                "Id": app_id,
                "ApplicantSerialNo": "1",
                "FirstName": data.get("FirstName", ""),
                "SurName": data.get("SurName", ""),
                "LastName": data.get("LastName", ""),
                "ServerDateOfBirth": data.get("DateOfBirth", "")[:10],
                "PlaceOfBirth": data.get("PlaceOfBirth", ""),
                "CountryOfBirthId": data.get("CountryOfBirthId", ""),
                "NationalityId": data.get("NationalityId", ""),
                "NationalityAtBirthId": data.get("NationalityAtBirthId", ""),
                "GenderId": data.get("GenderId", ""),
                "MaritalStatusId": data.get("MaritalStatusId", ""),
                "IsMinor": data.get("IsMinor", False),
                "PurposeOfJourneyId": data.get("PurposeOfJourneyId", ""),
                "MemberStateDestinationId": data.get("MemberStateDestinationId", ""),
                "MemberStateSecondDestinationId": data.get("MemberStateSecondDestinationId", ""),
                "MemberStateFirstEntryId": data.get("MemberStateFirstEntryId", ""),
                "MinorParentNationalityId": data.get("MinorParentNationalityId", ""),
                "PassportType": data.get("PassportType", ""),
                "IssueCountryId": data.get("IssueCountryId", ""),
                "ServerPassportIssueDate": data.get("IssueDate", "")[:10],
                "ServerPassportExpiryDate": data.get("ExpiryDate", "")[:10],
                "ServerTravelDate": data.get("TravelDate", "")[:10],
                "IssuePlace": data.get("IssuePlace", ""),
                "HomeAddressCountryId": data.get("HomeAddressCountryId", ""),
                "HasOtherResidenceship": data.get("HasOtherResidenceship", ""),
                "IsVisaIssuedBefore": data.get("IsVisaIssuedBefore", ""),
                "PreviousFingerPrintStatus": data.get("PreviousFingerPrintStatus", ""),
                "MinorParentSurname": data.get("MinorParentSurname", ""),
                "MinorParentFirstName": data.get("MinorParentFirstName", ""),
                "MinorParentLastName": data.get("MinorParentLastName", ""),
                "MinorParentAddress": data.get("MinorParentAddress", ""),
                "NationalIdentityNumber": data.get("NationalIdentityNumber", ""),
                "HomeAddressLine1": data.get("HomeAddressLine1", ""),
                "HomeAddressLine2": data.get("HomeAddressLine2", ""),
                "HomeAddressState": data.get("HomeAddressState", ""),
                "HomeAddressCity": data.get("HomeAddressCity", ""),
                "HomeAddressPostalCode": data.get("HomeAddressPostalCode", ""),
                "HomeAddressContactNumber": data.get("HomeAddressContactNumber", ""),
                "OtherResidenceshipPermitNumber": data.get("OtherResidenceshipPermitNumber", ""),
                "EmployerName": data.get("EmployerName", ""),
                "EmployerPhone": data.get("EmployerPhone", ""),
                "EmployerAddress": data.get("EmployerAddress", ""),
                "CurrentOccupationId": data.get("CurrentOccupationId", ""),
                "NumberOfEntriesRequested": data.get("NumberOfEntriesRequested", ""),
                "IntendedStayDuration": data.get("IntendedStayDuration", ""),
                "PreviousVisaNumber": data.get("PreviousVisaNumber", ""),
                "PreviousVisaIssuedCountryId": data.get("PreviousVisaIssuedCountryId", ""),
                "FinalDestinationIssuedByCountryId": data.get("FinalDestinationIssuedByCountryId", ""),
                "BlsInvitingAuthority": data.get("BlsInvitingAuthority", "1"),
                "InvitingAuthorityName": data.get("InvitingAuthorityName", ""),
                "InvitingCountryId": data.get("InvitingCountryId", ""),
                "InvitingCity": data.get("InvitingCity", ""),
                "InvitingZipCode": data.get("InvitingZipCode", ""),
                "InvitingAddress": data.get("InvitingAddress", ""),
                "InvitingEmail": data.get("InvitingEmail", ""),
                "InvitingContactNo": data.get("InvitingContactNo", ""),
                "InvitingFaxNo": data.get("InvitingFaxNo", ""),
                "InvitingContactName": data.get("InvitingContactName", ""),
                "InvitingContactSurname": data.get("InvitingContactSurname", ""),
                "InvitingContactCountryId": data.get("InvitingContactCountryId", ""),
                "InvitingContactCity": data.get("InvitingContactCity", ""),
                "InvitingContactZipCode": data.get("InvitingContactZipCode", ""),
                "InvitingContactAddress": data.get("InvitingContactAddress", ""),
                "InvitingContactContactNo": data.get("InvitingContactContactNo", ""),
                "InvitingContactEmail": data.get("InvitingContactEmail", ""),
                "InvitingContactFaxNo": data.get("InvitingContactFaxNo", ""),
                "CostCoveredById": data.get("CostCoveredById", ""),
                "MeansOfSupportId": data.get("MeansOfSupportId", ""),
                "OtherCitizenSurname": data.get("OtherCitizenSurname", ""),
                "OtherCitizenFirstName": data.get("OtherCitizenFirstName", ""),
                "OtherCitizenNationalityId": data.get("OtherCitizenNationalityId", ""),
                "OtherCitizenDocumentNumber": data.get("OtherCitizenDocumentNumber", ""),
                "OtherCitizenFamilyRelationshipId": data.get("OtherCitizenFamilyRelationshipId", ""),
            }
        else:
            default_dict = {
                "PassportNo": account.get("passportNO"),
                "ParentId": pid,
                "ApplicantId": app_id,
                "Id": app_id,
                "ApplicantSerialNo": "1",
                "FirstName": account.get("name"),
                "SurName": account.get("SurName"),
                "LastName": account.get("xing"),
                "ServerDateOfBirth": account.get("birthday").replace("/", "-"),
                "PlaceOfBirth": account.get("bornplace", "上海/SHANGHAI").split("/")[-1],
                "CountryOfBirthId": account.get("BirthCountry", "5e44cd63-68f0-41f2-b708-0eb3bf9f4a72"),
                "NationalityId": account.get("BirthCountry", "5e44cd63-68f0-41f2-b708-0eb3bf9f4a72"),
                "NationalityAtBirthId": account.get("BirthCountry", "5e44cd63-68f0-41f2-b708-0eb3bf9f4a72"),
                "GenderId": account.get("GenderId", genderData[account.get("gender", "男")]),
                "MaritalStatusId": account.get("MaritalStatusId", maritalStatusData[account.get("maritalStatus", "C").upper()]["Id"]),
                "IsMinor": account.get("IsMinor", False),
                "PurposeOfJourneyId": account.get("PurposeOfJourneyId", journeyPurposeData[account.get("visaTypeCode", "Tourism")].get("Id")),
                "MemberStateDestinationId": account.get("dest", "0cf9da11-cb76-4566-81e5-8628d5488e3c"),
                "MemberStateSecondDestinationId": account.get("MemberStateSecondDestinationId", ""),
                "MemberStateFirstEntryId": account.get("MemberStateFirstEntryId", ""),
                "MinorParentNationalityId": account.get("MinorParentNationalityId", "5e44cd63-68f0-41f2-b708-0eb3bf9f4a72"),
                "PassportType": account.get("PassportType", "0a152f62-b7b2-49ad-893e-b41b15e2bef3"),  # shengen_visa
                "IssueCountryId": account.get("IssueCountryId", "5e44cd63-68f0-41f2-b708-0eb3bf9f4a72"),
                "ServerPassportIssueDate": account.get("passportDate").replace("/", "-"),
                "ServerPassportExpiryDate": account.get("expiredDT").replace("/", "-"),
                "ServerTravelDate": account.get("travelDate").replace("/", "-"),
                "IssuePlace": account.get("signLocation").split("/")[-1],
                "HomeAddressCountryId": account.get("BirthCountry", "5e44cd63-68f0-41f2-b708-0eb3bf9f4a72"),
                "HasOtherResidenceship": account.get("HasOtherResidenceship", False),
                "IsVisaIssuedBefore": False,
                "PreviousFingerPrintStatus": 2,  # 豁免理由：  0: 过去59个月签证打印件 1：法律原因 2：无原因
                "BlsInvitingAuthority": "1",  # 申请人详细信息 0：公司机构 1：酒店零时住所 2：邀请人
                "MinorParentSurname": "",
                "MinorParentFirstName": "",
                "MinorParentLastName": "",
                "MinorParentAddress": "",
                "NationalIdentityNumber": "",
                "HomeAddressLine1": "",
                "HomeAddressLine2": "",
                "HomeAddressState": "",
                "HomeAddressCity": "",
                "HomeAddressPostalCode": "",
                "HomeAddressContactNumber": "",
                "OtherResidenceshipPermitNumber": "",
                "EmployerName": "",
                "EmployerPhone": "",
                "EmployerAddress": "",
                "CurrentOccupationId": "",
                "NumberOfEntriesRequested": "",
                "IntendedStayDuration": "",
                "PreviousVisaNumber": "",
                "PreviousVisaIssuedCountryId": "",
                "FinalDestinationIssuedByCountryId": "",
                "InvitingAuthorityName": "",
                "InvitingCountryId": "",
                "InvitingCity": "",
                "InvitingZipCode": "",
                "InvitingAddress": "",
                "InvitingEmail": "",
                "InvitingContactNo": "",
                "InvitingFaxNo": "",
                "InvitingContactName": "",
                "InvitingContactSurname": "",
                "InvitingContactCountryId": "",
                "InvitingContactCity": "",
                "InvitingContactZipCode": "",
                "InvitingContactAddress": "",
                "InvitingContactContactNo": "",
                "InvitingContactEmail": "",
                "InvitingContactFaxNo": "",
                "CostCoveredById": "",
                "MeansOfSupportId": "",
                "OtherCitizenSurname": "",
                "OtherCitizenFirstName": "",
                "OtherCitizenNationalityId": "",
                "OtherCitizenDocumentNumber": "",
                "OtherCitizenFamilyRelationshipId": "",
            }
        return default_dict
    except Exception as e:
        logger.error(f"appointment_detail error: {e.args[0]}")
        return None


def extract_appointment_detail_dict(html_string):
    # 使用正则表达式提取 JSON 字符串
    match = re.search(r"var primaryApplicant\s*=\s*({.*?});", html_string, re.DOTALL)

    if match:
        json_str = match.group(1)
        # 解析 JSON 字符串为字典
        primary_applicant_dict = json.loads(json_str)
        # print(primary_applicant_dict)
        return primary_applicant_dict
    else:
        # print("未找到 JSON 字符串")
        return None


if __name__ == "__main__":
    files_path = [
        "temp.html",
    ]
    for file_path in files_path:
        with open(file_path, "r") as fs:
            html_doc = fs.read()
        date_soup = BeautifulSoup(html_doc, "html.parser")
        day_key, slot_key, res_data, uri_post_date = bypass_date_appointment_verify(date_soup)
        form_dict_date = {_.get("name"): _.get("value") for _ in date_soup.select("input")}
        print(extract_func_params(html_doc))

    print(form_dict_date)
