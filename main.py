from spain_visa_registe import user_registe
from spain_visa_login import user_login
from spain_visa_update_profile import update_user_profile
from extension.logger import logger
import requests
from tool import generate_proxy_session, generate_phone_number, generate_random_email, generate_passport_number
from config import headers

from threading import Thread
from queue import Queue
from typing import Callable
from uuid import uuid4
import time
import json

DEFAULTWORKER = 4


class ThreadManager:
    # def __init__(self, queue: Queue, call: Callable, threads: int = DEFAULTWORKER) -> None:
    def __init__(self, call: Callable, threads: int = DEFAULTWORKER) -> None:
        self._threads = threads
        self._queue = Queue()
        self._call = call

    @logger.catch
    def call_with_wapper(self, task):
        self._call(task)

    def worker(self):
        while True:
            time.sleep(0.002)
            task = self._queue.get()
            self.call_with_wapper(task)

    @logger.catch
    def add_task(self, task):
        logger.info(f"Queue Size: {self._queue.qsize()}")
        self._queue.put(task)

    def start(self):
        for _ in range(self._threads):
            Thread(target=self.worker).start()


# 检测登录cookie有效时长，429频率
def test_cookie_time_interval():
    interval = 1190
    proxy_id = generate_proxy_session()
    proxy = f"600B7C99D4B1A09E-residential-country_CN-sense_cloudflare-r_10m-s_{proxy_id}:<EMAIL>:24125"
    proxy_dict = {"http": f"http://{proxy}", "https": f"http://{proxy}"}
    session = requests.session()
    session.headers.update(headers)
    session.proxies.update(proxy_dict)
    user = {"email": "<EMAIL>", "password": "Tjk925616@"}
    while True:
        try:
            login_flag, login_res = user_login(user, session)
        except Exception as e:
            login_flag = False
            logger.error(e)

        check_finish = False
        if login_flag:
            while True:
                time.sleep(interval)
                try:
                    session.headers.update({"referer": "https://spain.blscn.cn/chn/home/<USER>"})
                    res_page_home = session.get(
                        "https://spain.blscn.cn/CHN/appointment/manageapplicant?alert=true",
                        verify=False,
                        timeout=15,
                        allow_redirects=False,
                    )
                    logger.debug(f"cookie刷新间隔：{str(interval)}")
                    if res_page_home.status_code != 200:
                        logger.error(f"code:{res_page_home.status_code}, {res_page_home.text}")
                        with open("cookie_test.json", "w") as f:
                            json.dump({"time": interval, res_page_home.status_code: res_page_home.text}, f, ensure_ascii=False)
                        check_finish = True
                        break

                    interval = interval + 120
                except Exception as e:
                    logger.error(e)
        else:
            proxy_id = generate_proxy_session()
            proxy = f"600B7C99D4B1A09E-residential-country_CN-sense_cloudflare-r_10m-s_{proxy_id}:<EMAIL>:24125"
            proxy_dict = {"http": f"http://{proxy}", "https": f"http://{proxy}"}
            session.proxies.update(proxy_dict)

        if check_finish:
            break
    print("testing end")


# 检测登录cookie有效时长，429频率
def test_429_time_interval():
    interval = 60
    proxy_id = generate_proxy_session()
    proxy = f"600B7C99D4B1A09E-residential-country_CN-sense_cloudflare-r_10m-s_{proxy_id}:<EMAIL>:24125"
    proxy_dict = {"http": f"http://{proxy}", "https": f"http://{proxy}"}
    session = requests.session()
    session.headers.update(headers)
    session.proxies.update(proxy_dict)
    user = {"email": "<EMAIL>", "password": "Tjk925616@"}
    while True:
        try:
            login_flag, login_res = user_login(user, session)
        except Exception as e:
            login_flag = False
            logger.error(e)
        check_end = False
        if login_flag:
            while True:
                time.sleep(interval)
                try:
                    session.headers.update({"referer": "https://spain.blscn.cn/chn/home/<USER>"})
                    res_page_home = session.get("https://spain.blscn.cn/CHN/appointment/manageapplicant?alert=true", verify=False, timeout=15)
                    logger.debug(f"429刷新间隔：{str(interval)}")
                    if res_page_home.status_code != 200:
                        logger.error(f"code:{res_page_home.status_code}, {res_page_home.text}")
                        with open("429_test.json", "w") as f:
                            json.dump({"time": interval, res_page_home.status_code: res_page_home.text}, f, ensure_ascii=False)
                        check_end = True
                        break

                    interval = interval - 1
                except Exception as e:
                    logger.error(e)
        else:
            proxy_id = generate_proxy_session()
            # proxy = f"76F4CB1BA075D88C-residential-country_CN-r_120m-s_{proxy_id}:<EMAIL>:24125"
            proxy = f"600B7C99D4B1A09E-residential-country_CN-sense_cloudflare-r_10m-s_{proxy_id}:<EMAIL>:24125"
            proxy_dict = {"http": f"http://{proxy}", "https": f"http://{proxy}"}
            session.proxies.update(proxy_dict)

        if check_end:
            break
    print("test end")


if __name__ == "__main__":
    bg_worker = Thread(target=test_cookie_time_interval)
    bg_worker_429 = Thread(target=test_429_time_interval)

    bg_worker.start()
    bg_worker_429.start()

    bg_worker.join()
    bg_worker_429.join()
    print("end test")

    # proxy_id = generate_proxy_session()
    # proxy = f"76F4CB1BA075D88C-residential-country_CN-r_120m-s_{proxy_id}:<EMAIL>:24125"
    # proxy_dict = {"http": f"http://{proxy}", "https": f"http://{proxy}"}
    # session = requests.session()
    # session.headers = headers
    # session.proxies = proxy_dict
