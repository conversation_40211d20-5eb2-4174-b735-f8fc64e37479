import requests
import time
from config import headers
from copy import deepcopy
from user_manager import get_users_with_queue_name, spain_user_field
from tool import get_new_proxy, get_user_head_img, timmer

@timmer
def verify_user_head_img(user):
    session = requests.session()
    session.headers = deepcopy(headers)
    proxy = get_new_proxy()
    proxy_dict = {"http": proxy, "https": proxy}
    session.proxies.update(proxy_dict)
    session.headers.update(
        {
            "requestverificationtoken": "CfDJ8ExjRtuoEK1GjHvsUsGg4ZxrZIiOVePGscdiW18F16aSWboB3-Cf4beYAViMgaNyWYVuaQsHlTZ4fiBmcKh8x5SeBQ_cKbX5WEKzFPLWGqSZtjhbc_vfqhIi6WHEQFZMAGdQCY16YsrRyv4FfrHdv1FaFdclBL4PhDfXHRX-nCC8uWFv20Cj850GF4Ty5fM0Yw",
            "x-requested-with": "XMLHttpRequest",
        }
    )
    img_head_bytes = get_user_head_img(user)
    if not img_head_bytes:
        return False
    url = "https://spain.blscn.cn/CHN/appointment/UploadApplicantPhoto"
    try:
        res_upload = session.post(url, files={"file": (f"{user['passportNO']}.png", img_head_bytes, "image/jpeg")}, timeout=5)
        if res_upload.status_code == 200:
            # if res_upload.json()["success"]:
            # image_id = res_upload.json()["fileId"]
            print(res_upload.json())
        else:
            print(res_upload.status_code)
    except Exception as e:
        print(e)


if __name__ == "__main__":
    all_users = get_users_with_queue_name(spain_user_field)
    for u in all_users[:]:
        if u['passportNO'] in ['*********','*********']:
            verify_user_head_img(u)
