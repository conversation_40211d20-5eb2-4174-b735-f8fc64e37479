from typing import Callable
import threading
from queue import Queue
from .logger import logger
import time

class ThreadManager:
    # def __init__(self, queue: Queue, call: Callable, threads: int = DEFAULTWORKER) -> None:
    def __init__(self, call: Callable, threads: int = 4) -> None:
        self._threads = threads
        self._queue = Queue()
        self._call = call
        self.all_threads = []

    @logger.catch
    def call_with_wapper(self, task):
        self._call(task)

    def worker(self):
        while True:
            time.sleep(0.002)
            task = self._queue.get()
            self.call_with_wapper(task)

    def join(self):
        for t in self.all_threads:
            t.join()

    @logger.catch
    def add_task(self, task):
        logger.info(f"Queue Size: {self._queue.qsize()}")
        self._queue.put(task)

    def start(self):
        for _ in range(self._threads):
            t = threading.Thread(target=self.worker).start()
            self.all_threads.append(t)
