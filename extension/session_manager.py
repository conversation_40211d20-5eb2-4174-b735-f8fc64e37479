# import requests
from curl_cffi import requests
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter
from copy import deepcopy
from config import headers
from tool import get_random_user_agent

# 设置重试策略
retries = Retry(
    total=2,  # 总共重试次数
    read=3,
    connect=3,
    backoff_factor=0.1,  # 重试间隔时间因子
    status_forcelist=[502, 503, 500],  # 需要重试的状态码
    allowed_methods=["POST", "GET"],  # 需要重试的请求方法
)

adapter = HTTPAdapter(max_retries=retries)
header_default = deepcopy(headers)
header_default["user-agent"] = get_random_user_agent()

def create_session(proxy=None, cookie_dict=None, header=header_default):
    session = requests.Session(impersonate="chrome131")
    proxy_dict = {"http": proxy, "https": proxy}
    if proxy_dict:
        session.proxies.update(proxy_dict)
    if cookie_dict:
        session.cookies.update(cookie_dict)
    if header:
        session.headers.update(header)  # 将重试策略应用到会话对象
    # session.mount("http://", adapter)
    # session.mount("https://", adapter)
    return session
