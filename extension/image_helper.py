from PIL import Image
from io import BytesIO


def compress_image_to_target(original_bin, target_bytes=200 * 1024, resize_allowed=False):
    """
    压缩二进制图片到指定大小

    参数:
        original_bin (bytes): 原始图片二进制数据
        target_bytes (int): 目标大小(字节)
        resize_allowed (bool): 是否允许调整尺寸

    返回:
        bytes: 压缩后的二进制数据，如无法达到目标返回尽可能小的数据
    """
    # 转换为Pillow图像对象
    with BytesIO(original_bin) as input_buffer:
        try:
            img = Image.open(input_buffer)
            img = img.convert("RGB")  # 转换为RGB兼容JPEG
        except Exception as e:
            raise ValueError("无效的图片数据") from e

    # 阶段1：仅调整质量

    best_quality = 100
    best_output = None
    # 二分法查找最佳质量
    while True:
        buffer = BytesIO()
        img.save(buffer, format="JPEG", quality=best_quality, optimize=True)
        current_size = buffer.tell()

        if current_size <= target_bytes:
            best_output = buffer.getvalue()
            break
        else:
            best_quality = best_quality - 10  # 需要降低品质

    if best_output is not None:
        return best_output

    # 阶段2：调整尺寸
    if not resize_allowed:
        return img.tobytes()  # 返回原始数据或抛出异常

    # 使用最低质量获取基准大小
    buffer = BytesIO()
    img.save(buffer, format="JPEG", quality=20, optimize=True)
    base_size = buffer.tell()

    if base_size <= target_bytes:
        return buffer.getvalue()

    # 计算需要缩放的比率（假设尺寸与文件大小平方根相关）
    ratio = (target_bytes / base_size) ** 0.5
    new_width = int(img.width * ratio)
    new_height = int(img.height * ratio)

    # 确保最小尺寸为1
    new_width = max(1, new_width)
    new_height = max(1, new_height)

    resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

    # 再次尝试压缩
    buffer = BytesIO()
    resized_img.save(buffer, format="JPEG", quality=20, optimize=True)

    return buffer.getvalue()



if __name__ == "__main__":
    # 示例：读取图片并压缩
    with open("extension/1.png", "rb") as f:
        original_data = f.read()

    # 压缩到50KB (50*1024=51200 bytes)
    compressed_data = compress_image_to_target(original_data)

    # 保存结果
    with open("extension/output.jpg", "wb") as f:
        f.write(compressed_data)
