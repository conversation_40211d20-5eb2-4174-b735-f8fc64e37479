import requests
import random
import sys
import time
import threading
import json
from queue import Queue
from urllib3.util.retry import Retry
from bs4 import BeautifulSoup
import datetime
from copy import deepcopy

from spain_visa_appointment_date_open import book_appointment
from extension.logger import logger
from extension.session_manager import create_session
from tool import send_wx_msg, get_random_user_agent, send_dd_msg, area_map
from user_manager import save_user_2_redis_queue, get_users_with_queue_name, publish_redis_msg, spain_faker_scaning, save_logs
from config import url_host, headers
from bypass_book_appointment_verify import locationData, AppointmentCategoryIdData

user_queue = Queue()

url_available_date = "/CHN/BLSAppointment/GetAvailableAppointmentDates"

## 查询可预约的日期范围
def check_availabe_day(user_info, session, start_url):
    try:
        url_appointment_detail = url_host + start_url
        res_visa_detail = session.get(url_appointment_detail, verify=False, timeout=15)
        if res_visa_detail.status_code != 200:
            logger.error(f"#放号查询#选时间页面错误。{user_info['email']}:{res_visa_detail.status_code}: {res_visa_detail.text[:100]}")
            return False, []

        res_visa_detail_html = res_visa_detail.text
        soup = BeautifulSoup(res_visa_detail_html, "html.parser")
        forms = soup.find_all("form")
        if len(forms) == 0:
            tips = ""
            try:
                tips = soup.select(".alert-danger")[0].text
            except Exception:
                pass
            logger.error(f"##放号查询##{user_info.get('email')}-{user_info.get('passportNO')}预约表单为空：{tips}")
            return False, []
        # 查找第一个Form
        from_1 = list(filter(lambda x: x.attrs.get("id") == "myForm", forms))[0]
        elments_input = from_1.select("input")
        form_dict = {_.get("name"): _.get("value") for _ in elments_input}
        form_dict["X-Requested-With"] = "XMLHttpRequest"
        form_dict["AppointmentDetailsList"] = []
        form_dict.pop(None, None)

        session.headers.update({"requestverificationtoken": form_dict.get("__RequestVerificationToken")})
        session.headers.update({"referer": url_appointment_detail})

        # 选合适的预约日期 part1
        # 选择签发地 这里作用就是从 AppointmentCategoryIdData 中取到 MissionId，VIP和Normal的领事馆是一样的
        user_location = user_info.get("centerCode", "SHANGHAI").upper()
        loation_item = list(filter(lambda x: x.get("Code") == user_location, locationData))
        location_id = loation_item[0].get("Id")
        location_id = form_dict.get("LocationId", location_id)
        appointment_items = list(filter(lambda x: x.get("LegalEntityId") == location_id, AppointmentCategoryIdData))
        appointment_item = appointment_items[0]
        mission_id = appointment_item.get("MissionId")
        data = {
            "locationId": form_dict.get("LocationId", location_id),
            "categoryId": form_dict.get("AppointmentCategoryId"),
            "visaType": form_dict.get("VisaType", "099a0161-b428-4a10-bb1e-639b7dee4fa0"),  # 申根签证
            "visaSubType": form_dict.get("VisaSubTypeId", "6a7bbf0d-217c-4bc1-a458-54f60bff4811"),  # 申根签证
            "applicantCount": form_dict.get("ApplicantsNo", 1),
            "dataSource": form_dict.get("DataSource", "WEB_BLS"),
            "missionId": mission_id,  # 领事馆 - 上海
        }
        url_date = url_host + url_available_date
        # 获取可预约的日期
        res_date = session.post(url_date, params=data, verify=False, timeout=15)
        if res_date.status_code != 200:
            logger.error(f"#放号查询#获取预约日期出错:{res_date.status_code}")
            return False, []
        available_day = res_date.json()
        min_day = available_day.get("min")[:10]
        max_day = available_day.get("max")[:10]
        open_day_arr = []
        try:
            open_days = list(filter(lambda x: x.get("SingleSlotAvailable", False), res_date.json().get("ad", [])))
            open_day_arr = [e["DateText"] for e in open_days]
        except Exception as e:
            logger.debug(f"#放号查询#可约日期 Error:{e.args[0]}")

        return min_day + "-" + max_day, open_day_arr
    except Exception as e:
        logger.error(f"查询可预约日期出错:{e.args[0]}")
        return False, []


# 定义一个线程函数，接受参数
# @timmer
def scaning_appointment(user, publish=True):
    header = deepcopy(headers)
    header["user-agent"] = get_random_user_agent()
    # 登录状态为否 跳过
    if not user.get("is_login", False):
        return None, None

    # 假设cookie过期时间是20分钟
    if time.time() - int(user.get("updateTime", 0)) > 1200:
        user["is_login"] = False
        save_user_2_redis_queue(user)
        return None, None

    update_time = datetime.datetime.fromtimestamp(int(user["updateTime"])).strftime("%m-%d %H:%M:%S")
    logger.debug(f"##放号查询开始## {user.get('email')}, center:{user.get('centerCode')}, update:{update_time}")

    cookie_dict = user["cookies"]
    proxy = user["proxy"]
    # proxy_dict = {"http": proxy, "https": proxy}

    # session = requests.session()
    # session.proxies.update(proxy_dict)
    # session.cookies.update(cookie_dict)
    # session.headers.update(header)

    session = create_session(proxy, cookie_dict, header)

    # 查询预约号
    user["dateVIP"] = True if user.get("acceptVIP", 0) == 1 else False
    # book_appointment 内部会更新 dateVIP 字段 表示是否真的扫了VIP号
    flag_book, res_book = book_appointment(user, session)
    user["is_login"] = flag_book
    if not flag_book:  # 出错了，查看http状态码是否是502，是的话不需要重新登录。
        try:
            if int(res_book) == 502:
                logger.error("##放号查询## error code 502, continue...")
                user["is_login"] = True
        except Exception as e:
            logger.warning(f"##放号查询## {user.get('email')},res:{res_book},err:{e.args[0]}")
    save_user_2_redis_queue(user)

    if not flag_book:
        return False, None
    else:  # 扫号成功了，发送通知到企微
        area = user.get("centerCode")
        visaType = user.get("visaTypeCode")
        isVIP = user.get("dateVIP", False)  # book_appointment 内部会更新 dateVIP 字段 表示是否真的扫了VIP号
        logger.info(f"##放号查询结果## {area_map.get(area.upper(), area)} {visaType} {' VIP ' if isVIP else 'Normal'}, 可约:{res_book.get('available', False)}, res:{res_book}")
        appointment_open_flag = res_book.get("success")
        appointment_book_avalible = res_book.get("available")
        return_uri = res_book.get("returnUrl", None)
        if appointment_open_flag and appointment_book_avalible:
            area = user.get("centerCode")
            flag_days, all_days = check_availabe_day(user, session, return_uri)
            ## 发送redis消息，告知订阅者可以开始预约了
            info_dict = {"centerCode": area, "dates": all_days, "isVIP": isVIP, "visaType": visaType, "country": "spain", "updateTime": time.time()}
            redis_msg = json.dumps(info_dict)
            publish_redis_msg(redis_msg)
            # 发通知
            if publish:
                all_days_str = " | ".join(date_text[5:] for date_text in all_days)
                tip_str = f"#放号查询# 西班牙{area_map.get(area.upper(), area)} {visaType} {' VIP ' if isVIP else 'Normal'} 放号。可约日期: {all_days_str}"
                ## 发送微信 钉钉消息提醒
                logger.success(tip_str)
                send_dd_msg(tip_str)
                save_logs(user, info_dict)
                # send_wx_msg(tip_str, area + str(isVIP))

            if flag_days:  # 查询到开放日期就暂停一会，否则马上开始下一个
                time.sleep(2)
            else:  # 日期扫描出错了，马上开始下一个
                return False, None
        else:
            info_dict = {"centerCode": area, "dates": [], "isVIP": isVIP, "visaType": visaType, "country": "spain", "updateTime": time.time()}
            save_logs(user, info_dict)
        return session, return_uri


def work_scan_appointment_only():
    while not user_queue.empty():
        user = user_queue.get()
        pause, _ = scaning_appointment(user)
        if pause:
            time.sleep(random.uniform(15, 30))  # 控制下节奏
        user_queue.task_done()


def start_scaning(thread_count=2):
    thread_count = 1
    logger.info("#扫号#用户扫号....")
    while True:
        users = get_users_with_queue_name(spain_faker_scaning)
        login_users = list(filter(lambda x: x.get("is_login", False), users))
        users = login_users
        if len(users) <= 0:
            logger.debug("扫号队列无用户已登录")
            time.sleep(5)
            continue

        for user in users:
            user_queue.put(user)

        work_start = time.time()
        threads = []
        # 使用 for 循环创建多线程并传入参数
        for arg in range(thread_count):
            thread = threading.Thread(target=work_scan_appointment_only, daemon=True)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        work_finish = time.time()
        t_interval = int(work_finish - work_start)
        logger.debug(f"##完成 {len(users)} 个用户扫号, 耗时：{t_interval}s")
        time.sleep(5)

def thread_worker(centerCode="SHANGHAI", visaType="Tourism", interval=3):
    while True:
        all_users = get_users_with_queue_name()
        users_ = list(filter(lambda x: x.get("centerCode").upper() == centerCode and x["visaTypeCode"] == visaType, all_users))
        # random.shuffle(users_) # 打乱
        for user in users_:
            pause, _ = scaning_appointment(user)
            if pause:  # 查询流程成功了就暂停一下。
                time.sleep(interval)  # 控制下节奏
        time.sleep(2)
        
def start_scaning_v2(centerCode="SHANGHAI"):
    logger.info(f"#扫号# 西班牙 {centerCode}")

    thread_business = threading.Thread(target=thread_worker, args=(centerCode, "Business", 3), daemon=True)
    thread_business.start()

    thread_tourism = threading.Thread(target=thread_worker, args=(centerCode, "Tourism", 3), daemon=True)
    thread_tourism.start()

    while True:
        time.sleep(20)


if __name__ == "__main__":
    # 获取外部参数
    args = sys.argv[1:]
    thread_count = int(args[0]) if len(args) > 0 else 1
    # time.sleep(20)
    start_scaning(thread_count)
