# from curl_cffi import requests as curl
from user_manager import get_email_otp, get_users_with_queue_name, spain_user_field, move_user_2_queue, spain_error_users
from extension.logger import logger
import random
from bypass_login_page import extract_form_data_from_html
from config import wx_hook_urls, url_host
from bypass_verify_code import bypass_verify_code_pipeline
from tool import get_country_item, send_dd_msg, send_wx_msg
import requests
from tool import update_booking_status, BOOK_STATUS

user_agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

url_login_home = "https://spain.blscn.cn/CHN/account/login"
url_registe_home = "https://spain.blscn.cn/CHN/account/RegisterUser"
url_passsport_list = "https://spain.blscn.cn/CHN/query/GetLOVIdNameList?lovType=BLS_PASSPORT_TYPE"
url_send_email_otp = "https://spain.blscn.cn/CHN/account/SendRegisterUserVerificationCode"
url_registe_submit = "https://spain.blscn.cn/CHN/Account/RegisterUser"


def get_country_item_v(req_session, country_code="CHN"):
    return {
        "Name": "China",
        "Code": "CHN",
        "Id": "5e44cd63-68f0-41f2-b708-0eb3bf9f4a72",
        "CreatedDate": "0001-01-01T00:00:00",
        "LastUpdatedDate": "0001-01-01T00:00:00",
        "SequenceOrder": 1,
        "DataAction": 0,
        "Status": 0,
        "VersionNo": 0,
    }


def get_passport_item(req_session, passport_type_code="BLS_ORDINARY_PASSPORT"):
    """
    example return value
    {
        "Name": "Ordinary Passport",
        "Code": "BLS_ORDINARY_PASSPORT",
        "Id": "0a152f62-b7b2-49ad-893e-b41b15e2bef3",
    }
    """
    # 默认匹配 rdinary Passport
    # for _ in range(20):
    #     res = req_session.get(url_passsport_list, verify=False, timeout=15)
    #     if res.status_code == 200:
    #         passsport_list = res.json()
    #         passport_type = list(filter(lambda x: x.get("Code") == passport_type_code, passsport_list))
    #         return passport_type[0] if len(passport_type) > 0 else None
    #     time.sleep(0.1)
    return {
        "Name": "Ordinary Passport",
        "Code": "BLS_ORDINARY_PASSPORT",
        "Id": "0a152f62-b7b2-49ad-893e-b41b15e2bef3",
    }


def user_registe(account, session, real=True):
    try:
        user_email = account.get("email")
        user_pno = account.get("passportNO")
        logger.info(f"正在注册：{user_email} {account['visaTypeCode']} VIP:{account['acceptVIP']}")
        # 从登录页跳转到注册页
        res_home = session.get(url_login_home, verify=False, timeout=15)
        if res_home.status_code != 200:
            logger.error(f"{account['email']}，首页请求错误。{res_home.status_code}: {res_home.text}")
            return False, res_home

        # 注册页
        res_reg_page = session.get(url_registe_home, verify=False, timeout=15)
        if res_reg_page.status_code != 200:
            logger.error(f"{account['email']}，注册页请求错误。{res_reg_page.status_code}: {res_reg_page.text}")
            return False, res_reg_page

        # 获取注册表单默认 k v
        registe_form = extract_form_data_from_html(res_reg_page.text)
        country_item = get_country_item(session, account.get("countryCode", "CHN"))  # 签发国家
        passport_item = get_passport_item(session)  # 签证类型

        # 从html解析验证码链接并且完成验证码校验
        res_verify, res_ = bypass_verify_code_pipeline(session, res_reg_page.text)
        if not res_verify:
            logger.error(f"{account['email']}，验证码校验失败")
            return False, res_

        captchaData = res_verify.get("captcha")

        # 往邮箱发送验证码做二次确认
        send_email_verify_form = {
            "email": account.get("email"),
            "mobile": account.get("phone"),
            "isMobileVerify": False,
            "data": registe_form["SecurityCode"],
            "captchaData": captchaData,
            "captchaId": registe_form["CaptchaId"],
        }
        for _ in range(5):
            res_send_otp = session.post(url_send_email_otp, params=send_email_verify_form, verify=False, timeout=15)
            if res_send_otp.status_code != 200 or not res_send_otp.json().get("success"):
                logger.error(f"{account['email']}，邮箱确认码发送错误1。{res_send_otp.status_code}: {res_send_otp.text[:]}")
                continue
            break
        if res_send_otp.status_code != 200 or not res_send_otp.json().get("success"):
            logger.error(f"{account['email']}，邮箱确认码发送错误2。{res_send_otp.status_code}: {res_send_otp.text[:]}")
            return False, res_send_otp

        # 获得加密后的 部分信息
        encrypt_email = res_send_otp.json()["encryptEmail"]
        encrypt_mobile = res_send_otp.json()["encryptMobile"]
        security_code = res_send_otp.json()["securityCode"]
        # 从redis获取邮箱二次确认码
        email_otp = get_email_otp(account.get("email"))

        # 注册表单开始赋值
        registe_form["EncryptedEmail"] = encrypt_email
        registe_form["EncryptedMobile"] = encrypt_mobile
        registe_form["SecurityCode"] = security_code
        registe_form["EmailOtp"] = email_otp
        registe_form["CaptchaData"] = captchaData
        registe_form["PassportType"] = passport_item.get("Id")
        registe_form["CountryOfResidence"] = country_item.get("Id")
        registe_form["BirthCountry"] = country_item.get("Id")
        registe_form["Email"] = account.get("email")
        registe_form["Mobile"] = account.get("phone")
        registe_form["ServerDateOfBirth"] = account.get("birthday").replace("/", "-")
        registe_form["ServerPassportExpiryDate"] = account.get("expiredDT").replace("/", "-")
        registe_form["ServerPassportIssueDate"] = account.get("passportDate").replace("/", "-")
        # registe_form["SurName"] = account.get("xing")
        registe_form["FirstName"] = account.get("name")
        registe_form["LastName"] = account.get("xing")
        registe_form["DateOfBirth"] = account.get("birthday").replace("/", "-")
        registe_form["PassportNumber"] = account.get("passportNO")  # 用假的注册 防止重复
        registe_form["PassportIssueDate"] = account.get("passportDate").replace("/", "-")
        registe_form["PassportExpiryDate"] = account.get("expiredDT").replace("/", "-")
        registe_form["IssuePlace"] = account.get("signLocation").split("/")[-1]
        registe_form["X-Requested-With"] = "XMLHttpRequest"
        registe_form["MobileVerificationEnabled"] = False if registe_form["MobileVerificationEnabled"] == "False" else True

        res_registe = session.post(url_registe_submit, data=registe_form, verify=False, timeout=15)

        if res_registe.status_code == 200:
            if res_registe.json()["success"]:
                account["BirthCountry"] = registe_form["BirthCountry"]
                account["PassportType"] = registe_form["PassportType"]
                return True, account
            else:
                logger.error(f"注册信息校验失败。{account.get('chnname')} {user_email}:{user_pno},{res_registe.status_code}: {res_registe.text}")
                if real:  # and "Passport Number already exists" in res_registe.json().get("error"):  # 注册真实用户发生错误发个微信消息
                    url = random.choice(wx_hook_urls)
                    msg = f"#西班牙# 注册错误: {account.get('chnname')} {account.get('centerCode')} {account.get('visaTypeCode')}  {user_pno} {user_email}, 错误原因:{res_registe.json().get('error')}"
                    update_booking_status(account, BOOK_STATUS.REGISTE_ERROR, f"注册失败，{res_registe.json().get('error')}")
                    requests.post(url, json={"msgtype": "text", "text": {"content": msg}}, timeout=3)
                    send_dd_msg(msg)
                    if "Passport Number already exists" in res_registe.json().get("error"): 
                        account["status"] = "passport_exist"  # 无效的用户
                        move_user_2_queue(account, spain_error_users)  # 移动到错误队列
                return False, res_registe
        else:
            logger.error(f"注册表单失败。{user_email}:{user_pno},{res_registe.status_code}: {res_registe.text[:]}")
            return False, res_registe.text

    except Exception as e:
        logger.error(f"注册出错{account['email']}, error：{e}")
        return False, e


def registe_pipline():
    # 从redis读取用户信息
    # TODO 校验必要参数，不完整则丢弃，并发送通知告知销售联系客户补充信息
    pass


if __name__ == "__main__":
    all_users = get_users_with_queue_name(spain_user_field)
    # 注册新用户 或者 完成已注册用户的信息修改保存
    uncomplete_user = list(filter(lambda x: x["status"] == "pending", all_users))
