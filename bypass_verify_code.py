import re
from bs4 import BeautifulSoup
from collections import defaultdict
from extension.logger import logger
from tool import b64_api_bypass, timmer
import json


# 只保留每组中 z-index 最大的元素
def keep_max_zindex(elements):
    max_zindex_elements = {}
    for (left, top), elems in elements.items():
        max_zindex = -float("inf")
        max_zindex_elem = None

        for elem in elems:
            zindex = elem["styles"].get("z-index", "auto")
            if zindex == "auto":
                zindex = 0
            else:
                zindex = int(zindex)

            if zindex > max_zindex:
                max_zindex = zindex
                max_zindex_elem = elem

        if max_zindex_elem:
            max_zindex_elements[(left, top)] = max_zindex_elem

    return max_zindex_elements


# 分组函数：根据 left 和 top 值分组
def group_by_position(elements, element_id=""):
    grouped = defaultdict(list)
    for element_id, details in elements.items():
        styles = details["styles"]
        left = styles.get("left", "0px")
        top = styles.get("top", "0px")
        grouped[(left, top)].append(
            {
                "id": element_id,
                "classes": details["classes"],
                "styles": styles,
                "img_src": details.get("img_src"),
            }
        )
    return grouped


def filter_method_no_call(method_definition):
    if "try{returnfalse;" in method_definition.strip().replace(" ", "").replace("\n", ""):  # 方法没调用
        return False
    return True


def extract_show_imgs(soup, select_name=".captcha-img"):
    # 从 HTML 中提取所有 CSS 样式
    style_rules = ""
    for style in soup.find_all("style"):
        style_rules += style.string if style.string else ""

    # 从 HTML 中找出对应 ID 的元素、class 和样式
    id_to_class_and_style = {}
    # 真实的54张图片的ids
    el_imgs = soup.select(select_name)
    for el in el_imgs:
        el_id = el.parent.attrs.get("id")
        classes = " ".join(el.parent.get("class", [])).split()
        img_src = el.attrs.get("src")
        styles = {}
        for cls_ in classes:
            pattern = re.compile(rf"\.{re.escape(cls_)}\s*\{{(.*?)\}}", re.DOTALL)
            matches = pattern.findall(style_rules)

            # 合并样式
            for match in matches:
                for declaration in match.split(";"):
                    if declaration.strip():
                        property_name, _, value = declaration.partition(":")
                        styles[property_name.strip()] = value.strip()

        # 保存class和解析出来的styles
        id_to_class_and_style[el_id] = {
            "classes": classes,
            "styles": styles,  # 初始化为空，稍后将填充样式
            "img_src": img_src,
        }

    # 判断元素的显示状态
    visible_ids = set()
    for element_id, details in id_to_class_and_style.items():
        styles = details["styles"]
        # 根据最终样式判断 display 属性
        display = styles.get("display", "inline")
        # style 后生效
        if display in ["inline", "block"]:
            visible_ids.add(element_id)

    # 根据 left 和 top 分组
    grouped_elements = group_by_position({id: id_to_class_and_style[id] for id in visible_ids})
    # 只保留 z-index 最大的元素
    max_zindex_elements = keep_max_zindex(grouped_elements)

    # # 按九宫格位置（left 和 top）排序
    sorted_elements = sorted(
        max_zindex_elements.items(),
        key=lambda x: (int(x[0][0].replace("px", "")), int(x[0][1].replace("px", ""))),
    )

    # 打印最终结果
    res_imgs = dict()
    for (left, top), elem in sorted_elements:  # max_zindex_elements.items():
        el_iid = elem.get("id")
        res_imgs[el_iid] = elem.get("img_src")
    return res_imgs, style_rules


def extract_show_title(soup, select_name=".box-label", style_rules=""):
    # 从 HTML 中找出对应 ID 的元素、class 和样式
    id_to_class_and_style = {}
    # 全部标题
    el_titlts = soup.select(select_name)
    for el in el_titlts:
        el_id = el.attrs.get("id")
        classes = " ".join(el.get("class", [])).split()
        el_title = el.text
        styles = {}
        for cls_ in classes:
            pattern = re.compile(rf"\.{re.escape(cls_)}\s*\{{(.*?)\}}", re.DOTALL)
            matches = pattern.findall(style_rules)

            # 合并样式
            for match in matches:
                for declaration in match.split(";"):
                    if declaration.strip():
                        property_name, _, value = declaration.partition(":")
                        styles[property_name.strip()] = value.strip()

        # 保存class和解析出来的styles
        id_to_class_and_style[el_id] = {
            "classes": classes,
            "styles": styles,  # 初始化为空，稍后将填充样式
            "text": el_title,
        }

    # 判断元素的显示状态
    visible_ids = set()
    for element_id, details in id_to_class_and_style.items():
        styles = details["styles"]
        # 根据最终样式判断 display 属性
        display = styles.get("display", "inline")
        # style 后生效
        if display in ["inline", "block"]:
            visible_ids.add(element_id)

    max_zindex_elem = None
    max_zindex = -float("inf")
    for el_id, elem in id_to_class_and_style.items():
        zindex = elem["styles"].get("z-index", "auto")
        if zindex == "auto":
            zindex = 0
        else:
            zindex = int(zindex)

        if zindex > max_zindex:
            max_zindex = zindex
            max_zindex_elem = elem
    # print(max_zindex_elem.get("id"), max_zindex_elem.get("text"))
    return max_zindex_elem.get("id", None), max_zindex_elem.get("text", None)


@logger.catch
def extract_verify_code_link(html_doc):
    uri_pattern = re.compile(r"win\.iframeOpenUrl\s*=\s*'([^']*)'")
    match = uri_pattern.search(html_doc)
    link = match.group(1) if match else None
    return link


def extract_password_input(soup: BeautifulSoup, style_rules=""):
    # 从 HTML 中提取所有 CSS 样式

    # 从 HTML 中找出对应 ID 的元素、class 和样式
    id_to_class_and_style = {}
    el_inputs = soup.select("input")

    for el_input in el_inputs:
        input_type = el_input.attrs.get("type")
        if input_type != "password":
            continue

        label_id = el_input.attrs.get("id")
        # print(el_id, label_id)
        el_div = el_input.parent
        if not label_id:
            continue
        classes = " ".join(el_div.get("class", [])).split()
        # el_type = el.attrs.get("type")
        styles = {}
        for cls_ in classes:
            pattern = re.compile(rf"\.{re.escape(cls_)}\s*\{{(.*?)\}}", re.DOTALL)
            matches = pattern.findall(style_rules)

            # 合并样式
            for match in matches:
                for declaration in match.split(";"):
                    if declaration.strip():
                        property_name, _, value = declaration.partition(":")
                        styles[property_name.strip()] = value.strip()

        # 保存class和解析出来的styles
        id_to_class_and_style[label_id] = {
            "classes": classes,
            "styles": styles,  # 初始化为空，稍后将填充样式
            "id": label_id,
            "text": "",
        }

    # 判断元素的显示状态
    visible_ids = set()
    for element_id, details in id_to_class_and_style.items():
        styles = details["styles"]
        # 根据最终样式判断 display 属性
        display = styles.get("display", "inline")
        # style 后生效
        if "inline" in display or "block" in display:
            visible_ids.add((element_id, details["text"]))

    visible_ids = list(visible_ids)[0]
    res_ids = {x: "" for x in id_to_class_and_style.keys()}
    return visible_ids[0], res_ids


# @timmer
def bypass_verify_code_func(html_doc, method_name="VerifyRegister", pasword=""):
    try:
        soup = BeautifulSoup(html_doc, "html.parser")
        url = "/CHN/CaptchaPublic/SubmitCaptcha" if method_name == "VerifyRegister" else "/CHN/NewCaptcha/LoginCaptchaSubmit"

        # 0.01s耗时
        el_forms = soup.select("form")
        for el in el_forms:
            if el.get("id") == "captchaForm" and el.attrs.get("action"):
                url = el.attrs.get("action")
                break

        all_9_imgs, style_rules = extract_show_imgs(soup)
        if len(all_9_imgs.keys()) != 9:
            logger.error("验证码图片提取错误，重新加载验证码页面")
            return False, {}, -1, url

        # 提取真实的验证码数字
        title_id, text = extract_show_title(soup, ".box-label", style_rules)
        if not text:
            logger.error("验证码标题提取错误，重新加载验证码页面")
            return False, {}, -1, url

        answer_number = re.findall(r"\d+\.?\d*", text)[0]  # text.split("Please select all boxes with number ")[1].strip()

        SelectedImages = []
        unrecog_img = []  # 没识别出来的可能对可能错
        for img_id, img_bs64 in all_9_imgs.items():
            pic_number = b64_api_bypass(img_bs64)
            if pic_number == answer_number:  # or not pic_number: # 没识别出来默认是正确答案
                SelectedImages.append(img_id)
            if not pic_number:
                unrecog_img.append(img_id)

        if len(SelectedImages) < 3: # 一般验证码最少三个，少于三个就把没识别出来的算上
            SelectedImages = SelectedImages + unrecog_img

        elments_input = soup.select("input")
        form_dict = {_.get("name"): _.get("value") for _ in elments_input}
        ## 注册流程
        if method_name == "VerifyRegister":
            form_dict["SelectedImages"] = ",".join(SelectedImages)
            form_dict["X-Requested-With"] = "XMLHttpRequest"
            return True, form_dict, answer_number, url
        else:  # 登录流程
            e_id, all_ids = extract_password_input(soup, style_rules)
            all_ids[e_id] = pasword
            form_dict["SelectedImages"] = ",".join(SelectedImages)
            form_dict["ResponseData"] = json.dumps(all_ids)
            form_dict[e_id] = pasword
            return True, form_dict, answer_number, url
    except Exception as e:
        logger.error(f"验证码解析发生错误，{e}")
        return False, {}, -1, ""


def bypass_verify_code_pipeline(session, html_doc, url_host="https://spain.blscn.cn", method_name="VerifyRegister", proxy=""):
    # 提取验证码链接
    verify_code_link_uri = extract_verify_code_link(html_doc)  # if method_name == "VerifyRegister" else "/CHN/NewCaptcha/GenerateCaptcha"
    if not verify_code_link_uri:
        logger.error("验证码链接解析失败")
        return False, 403
    # 解析跳转验证码页面的链接
    verify_code_link = url_host + verify_code_link_uri
    res = 403
    for i in range(3):
        # 打开验证码页面
        res_verify_code = session.get(verify_code_link, verify=False, timeout=15)
        res = res_verify_code
        if res_verify_code.status_code != 200:
            logger.error(f"获取验证码页面错误{i+1}次。link：{verify_code_link}, code:{res_verify_code.status_code}, text:{res_verify_code.text[-400:]}")
            if res_verify_code.status_code == 403:
                return False, res_verify_code.status_code
            else:
                continue

        # step2 解析验证码图片
        verify_code_html = res_verify_code.text
        # with open('verify_code.html', 'w') as f:
        #     f.write(verify_code_html)
        code, verify_data, pic_number, uri = bypass_verify_code_func(verify_code_html, method_name)
        if not code:
            logger.error("验证码图片/标题识别失败")
            continue
            return False, res.status_code

        # step3 验证码识别
        url_captcha_submit = url_host + uri
        # 稳定代理IP
        res_verify = session.post(url_captcha_submit, data=verify_data, verify=False, timeout=15)
        # 代理IP不稳定
        # res_verify = requests.post(url_captcha_submit, data=verify_data, headers=session.headers, cookies=session.cookies, proxies=proxy_dict, verify=False, timeout=15)
        res = res_verify
        if res_verify.status_code != 200 or not res_verify.json().get("success"):
            # TODO  这里要reload一下验证码 尝试重新识别
            logger.error(f"验证码识别错误:{res_verify.status_code}: {res_verify.text[:100]}")
            if res_verify.status_code == 200:
                continue
            else:
                return False, res.status_code
        else:  # 验证码识别成功，返回json
            res_dict = res_verify.json()
            return res_dict, res.status_code

    return False, res


if __name__ == "__main__":
    files_path = [
        "htmls/验证码.html",
    ]
    for file_path in files_path:
        with open(file_path, "r") as fs:
            html_doc = fs.read()
        print("\n", file_path, ":")
        res = verify_code_link = bypass_verify_code_func(html_doc)
        print(res)
